package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.convertToString
import ai.friday.billpayment.adapters.dynamodb.CreditCardValidationDbRepository
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.Role.Code.ADMIN
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.account.isPossibleFraud
import ai.friday.billpayment.app.backoffice.FraudPreventionService
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.caf.isCafTransaction
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.app.integrations.DocumentValidationResponse
import ai.friday.billpayment.app.integrations.DocumentValidationService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.http.exceptions.HttpStatusException
import io.micronaut.http.server.types.files.SystemFile
import io.micronaut.security.annotation.Secured
import java.io.File
import java.io.InputStream
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/fraud-prevention")
@FridayMePoupe
class BackofficeFraudPreventionController(
    private val fraudPreventionService: FraudPreventionService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val documentValidationService: DocumentValidationService,
    private val creditCardScoreService: CreditCardScoreService,
    private val creditCardValidationDbRepository: CreditCardValidationDbRepository,
    private val accountRepository: AccountRepository,
    private val balanceService: BalanceService,
    private val closeAccountService: CloseAccountService,
    private val cafService: CafService,
) {
    @Get("/{accountId}/summary", produces = [MediaType.TEXT_PLAIN])
    fun getAccountSummary(@PathVariable accountId: String): SystemFile {
        return try {
            val prefix = "$accountId-SUMMARY-${getZonedDateTime().toEpochSecond()}"
            val suffix = ".txt"
            val file = File.createTempFile(prefix, suffix)
            val summary = fraudPreventionService.accountSummary(accountId = AccountId(accountId))
            file.writeText(summary)
            SystemFile(file).attach(prefix + suffix)
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", accountId), "getAccountSummary", e)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error creating file")
        }
    }

    @Get("/{accountIdValue}", produces = ["text/plain"])
    fun getFraudPrevention(@PathVariable accountIdValue: String): SystemFile {
        return try {
            val accountId = AccountId(accountIdValue)

            val builder = StringBuilder()

            val balance = balanceService.getAccountBalance(accountId)
            builder.appendLine("=== BALANCE ========")
            builder.appendLine(convertToString(balance.amount))
            builder.appendLine("")

            val documentValidation = buildDocumentValidationWithSelfie(accountId = accountId)
            builder.appendLine("=== SERPRO ========")
            builder.appendLine(documentValidation)
            builder.appendLine("")

            val summary = fraudPreventionService.accountSummary(accountId = accountId)
            builder.appendLine("=== SUMMARY ========")
            builder.appendLine(summary)
            builder.appendLine("")

            val prefix = "$accountIdValue-FRAUD-PREVENTION-${getZonedDateTime().toEpochSecond()}"
            val suffix = ".txt"
            val file = File.createTempFile(prefix, suffix)
            file.writeText(builder.toString())
            SystemFile(file).attach(prefix + suffix)
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", accountIdValue), "getAccountSummary", e)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error creating file")
        }
    }

    @Get("/{accountId}/documentValidationWithSelfie", produces = [MediaType.TEXT_PLAIN])
    fun getDocumentValidationWithSelfie(@PathVariable accountId: String): SystemFile {
        try {
            val prefix = "$accountId-DOCUMENT-VALIDATION-${getZonedDateTime().toEpochSecond()}"
            val suffix = ".txt"
            val file = File.createTempFile(prefix, suffix)
            file.writeText(buildDocumentValidationWithSelfie(AccountId(accountId)))
            return SystemFile(file).attach(prefix + suffix)
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", accountId), "getDocumentValidationWithSelfie", e)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error creating file")
        }
    }

    @Secured(ADMIN)
    @Put("/{accountId}/confirmFraud")
    fun confirmFraud(@PathVariable accountId: String): HttpResponse<*> {
        val logName = "confirmFraud"
        val markers = Markers.append("accountId", accountId)
        val accountRegister = try {
            accountRegisterRepository.findByAccountId(accountId = AccountId(accountId), checkOpenAccount = false)
        } catch (e: Exception) {
            LOG.error(markers.andAppend("error", e), logName)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error confirming fraud")
        }

        if (accountRegister.accountClosureDetails?.isPossibleFraud() == false) {
            LOG.error(markers.andAppend("error", "account was not possible fraud"), logName)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "account was not possible fraud")
        }

        closeAccountService.confirmFraud(
            accountRegister,
        ).getOrElse {
            LOG.error(markers.andAppend("error", it), logName)
            throw HttpStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "error confirming fraud")
        }
        LOG.info(markers, logName)
        return HttpResponse.noContent<Unit>()
    }

    private fun buildDocumentValidationWithSelfie(accountId: AccountId): String {
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        val selfieImageStream = accountRegisterRepository.getDocumentInputStream(accountRegisterData.uploadedSelfie!!)

        val documentValidation = validateWithSelfie(accountRegisterData, selfieImageStream).getOrElse {
            LOG.error(Markers.append("accountId", accountId.value), "getDocumentValidationWithSelfie", it)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error creating file")
        }

        return jacksonObjectMapper().writeValueAsString(documentValidation)
    }

    private fun validateWithSelfie(
        accountRegisterData: AccountRegisterData,
        selfieImageStream: InputStream,
    ): Either<Exception, DocumentValidationResponse> {
        return if (accountRegisterData.isCafTransaction()) {
            cafService.validateDocument(accountRegisterData.livenessId!!)
        } else if (accountRegisterData.documentInfo != null) {
            documentValidationService.validateWithSelfie(
                accountRegisterData.documentInfo,
                selfieImageStream,
            )
        } else {
            documentValidationService.validateWithSelfie(
                accountRegisterData.document!!,
                accountRegisterData.getName(),
                selfieImageStream,
            )
        }
    }

    @Get("/{accountId}/validateCreditCardOwnershipValidation/{bin}/{lastFourDigits}")
    fun validateCreditCardOwnership(
        @PathVariable accountId: String,
        @PathVariable bin: String,
        @PathVariable lastFourDigits: String,
        @Header("X-USE-CCSCORE") useCreditCardScore: String?,
    ): HttpResponse<*> {
        try {
            val account =
                accountRepository.findById(AccountId(accountId))

            if (useCreditCardScore == "1") {
                val request = CreditCardScoreValidationRequest(
                    cpf = account.document,
                    bin = bin,
                    lastFourDigits = lastFourDigits,
                    totalValue = "0",
                )

                val validation = creditCardScoreService.creditcardScore(
                    scoreRequest = request,
                )

                return HttpResponse.ok(validation)
            } else {
                val validation = creditCardValidationDbRepository.find(
                    cpf = account.document,
                    bin = bin,
                    lastFourDigits = lastFourDigits,
                )

                return HttpResponse.ok(validation)
            }
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", accountId), "validateCreditCardOwnershipValidation", e)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error validating credit card ownership")
        }
    }

    @Get("/{accountId}/cachedCreditCardOwnershipValidation{/bin}{/lastFourDigits}")
    fun getCachedCreditCardOwnershipValidation(
        @PathVariable accountId: String,
        @PathVariable bin: String?,
        @PathVariable lastFourDigits: String?,
    ): HttpResponse<*> {
        try {
            val account =
                accountRepository.findById(AccountId(accountId))

            val validation = creditCardValidationDbRepository.find(
                cpf = account.document,
                bin = bin,
                lastFourDigits = lastFourDigits,
            )

            return HttpResponse.ok(validation)
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", accountId), "cachedCreditCardOwnershipValidation", e)
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "error retrieving credit card ownership")
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeFraudPreventionController::class.java)
    }
}