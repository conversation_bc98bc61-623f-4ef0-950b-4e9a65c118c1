package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.adapters.api.builders.PixQrCodeDataTO
import ai.friday.billpayment.and
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.hasDeveloperEarlyAccess
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AddBillError
import ai.friday.billpayment.app.bill.AddBillError.ALREADY_PAID
import ai.friday.billpayment.app.bill.AddBillError.BILL_ALREADY_INCLUDED
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAlreadyLockedException
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillImageProvider
import ai.friday.billpayment.app.bill.BillNotIgnorableException
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillStatus.WAITING_APPROVAL
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillValidationException
import ai.friday.billpayment.app.bill.CreateBillError
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreateConcessionariaRequest
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.CreateInvoiceRequest
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.InvalidBillStateChangeException
import ai.friday.billpayment.app.bill.MemberNotAllowedException
import ai.friday.billpayment.app.bill.NewerBillExistsException
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.availablePaymentMethods
import ai.friday.billpayment.app.bill.checkExistsOneRecipient
import ai.friday.billpayment.app.bill.descriptionMaxLength
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.bill.getWarningCode
import ai.friday.billpayment.app.bill.isCreditCard
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MaintenanceServicesEnum
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.TedConfiguration
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.isValid
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.IgnoreRecurrenceErrors
import ai.friday.billpayment.app.recurrence.Range
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceResult
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonAnyGetter
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.annotation.Nullable
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Error
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.ConstraintViolationException
import jakarta.validation.Valid
import jakarta.validation.constraints.Digits
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size
import java.math.BigInteger
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import net.logstash.logback.marker.Markers.empty
import org.slf4j.Logger
import org.slf4j.LoggerFactory

private const val descriptionRegex = "^[^'|]{0,$descriptionMaxLength}\$"
private const val descriptionErrorMessage =
    "Description size must be up to 140 length and can't include | or ' character"

fun WalletBillCategory.toBillCategoryTO() = BillCategoryTO(
    billCategoryId = categoryId.value,
    name = name,
    icon = icon,
    enabled = enabled,
)

@Validated
@Secured(Role.Code.OWNER)
@Controller("/bill")
@FridayMePoupe
@Version("2")
class BillController(
    private val createBillService: CreateBillService,
    private val fichaCompensacaoService: FichaCompensacaoService,
    private val updateBillService: UpdateBillService,
    private val recurrenceService: BillRecurrenceService,
    private val pixKeyManagement: PixKeyManagement,
    private val accountService: AccountService,
    private val tedConfiguration: TedConfiguration,
    private val featuresRepository: FeaturesRepository,
    private val billImageProvider: BillImageProvider,
    private val billTOBuilder: BillTOBuilder,
    private val findBillService: FindBillService,
) {
    private val createPixHelper = CreatePixHelper(createBillService, billImageProvider, billTOBuilder, LOG)

    @Post("/concessionaria{?dryRun}")
    fun addConcessionariaBill(
        @Body @Valid
        addConcessionariaTO: AddConcessionariaTO,
        @QueryValue(defaultValue = "false") dryRun: Boolean,
        authentication: Authentication,
    ): HttpResponse<*> {
        val barCode = BarCode.ofDigitable(addConcessionariaTO.digitableLine)
        val dueDate = LocalDate.parse(addConcessionariaTO.dueDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        val concessionaria = CreateConcessionariaRequest(
            barcode = barCode,
            description = addConcessionariaTO.description,
            dueDate = dueDate,
            walletId = authentication.toWalletId(),
            source = authentication.getActionSource(),
            member = authentication.asWalletMember(),
        )
        val markers = append("request", concessionaria)
        return buildBoletoResponse(
            createBillService.createConcessionaria(
                request = concessionaria,
                dryRun = dryRun,
            ),
            authentication.asWalletMember(),
            dryRun,
            markers,
        )
    }

    @Post("/ficha-compensacao{?dryRun}")
    fun addFichaDeCompensacao(
        @Body @Valid
        addFichaDeCompensacaoTO: AddFichaDeCompensacaoTO,
        @QueryValue(defaultValue = "false") dryRun: Boolean,
        authentication: Authentication,
    ): HttpResponse<*> {
        val barCode = BarCode.ofDigitable(addFichaDeCompensacaoTO.digitableLine)
        val fichaDeCompensacao = CreateFichaDeCompensacaoRequest(
            barcode = barCode,
            description = addFichaDeCompensacaoTO.description,
            walletId = authentication.toWalletId(),
            source = authentication.getActionSource(),
            member = authentication.asWalletMember(),
        )
        val markers = append("request", fichaDeCompensacao)

        if (isBoletoInMaintenanceMode(authentication)) {
            return HttpResponse.status<ResponseTO>(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ResponseTO("5030", "Serviço em manutenção"))
        }

        return buildBoletoResponse(
            fichaCompensacaoService.createFichaDeCompensacao(
                request = fichaDeCompensacao,
                dryRun = dryRun,
            ),
            authentication.asWalletMember(),
            dryRun,
            markers,
        )
    }

    private fun isBoletoInMaintenanceMode(authentication: Authentication): Boolean {
        if (authentication.toAccountId().hasDeveloperEarlyAccess()) {
            return false
        }
        return featuresRepository.getAll().maintenanceServices.contains(MaintenanceServicesEnum.BOLETO)
    }

    @Put("/id/{billId}/description")
    fun updateDescription(
        @PathVariable billId: String,
        @Body @Valid
        updateDescription: UpdateDescriptionTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val member = authentication.asWalletMember()
        val markers = append("accountId", authentication.name)
            .andAppend("walletId", authentication.getWallet().id.value)
            .andAppend("billId", billId)
        LOG.info(markers, "UpdateDescription")

        val updateDescriptionResult = updateBillService.updateDescription(
            billId = BillId(billId),
            walletId = authentication.toWalletId(),
            member = member,
            description = updateDescription.description,
            actionSource = authentication.getActionSource(),
        )
        return updateDescriptionResult.map {
            HttpResponse.ok<Unit>()
        }.getOrElse {
            when (it) {
                is ItemNotFoundException -> {
                    LOG.warn(
                        markers.andAppend("httpStatus", HttpStatus.NOT_FOUND.code).andAppend("error", it.message),
                        "UpdateDescription",
                    )
                    StandardHttpResponses.billNotFound()
                }

                is MemberNotAllowedException -> {
                    LOG.warn(
                        markers.andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                        "UpdateDescription",
                    )
                    HttpResponse.status(HttpStatus.FORBIDDEN)
                }

                else -> {
                    LOG.error(
                        markers.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code).andAppend(
                            "error",
                            it.message,
                        ),
                        "UpdateDescription",
                    )
                    HttpResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
                }
            }
        }
    }

    @Put("/id/{billId}/amount")
    fun updateAmount(
        @PathVariable billId: String,
        @Body @Valid
        updateAmountTO: UpdateAmountTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
            .andAppend("walletId", authentication.getWallet().id.value)
            .andAppend("billId", billId)
        LOG.info(markers, "UpdateAmount")

        val updateAmountResult = if (updateAmountTO.range == Range.THIS_AND_FUTURE) {
            recurrenceService.updateAmount(
                billId = BillId(billId),
                amount = updateAmountTO.amount,
                actionSource = authentication.getActionSource(),
            )
        } else {
            updateBillService.updateAmount(
                billId = BillId(billId),
                amount = updateAmountTO.amount,
                actionSource = authentication.getActionSource(),
            )
        }
        markers.andAppend("result", updateAmountResult)

        return updateAmountResult.map {
            LOG.info(markers, "UpdateAmount")
            HttpResponse.noContent<Unit>()
        }.getOrElse { e ->
            markers.andAppend("exceptionMessage", e.message)
            if (e is BillValidationException) {
                LOG.error(markers, "UpdateAmount", e)
                StandardHttpResponses.badRequest(code = "40001", message = e.message ?: "")
            } else {
                LOG.error(markers, "UpdateAmount", e)
                StandardHttpResponses.serverError(message = e.message ?: "")
            }
        }
    }

    @Put("/id/{billId}/ignore{?range}")
    fun ignoreBill(
        @PathVariable billId: String,
        authentication: Authentication,
        @QueryValue range: Range? = Range.THIS,
    ): HttpResponse<*> {
        val marker = append("BillId", billId)
            .andAppend("range", range)
            .andAppend("accountId", authentication.toAccountId())
            .andAppend("walletId", authentication.getWallet().id.value)
            .andAppend("role", authentication.getActionSource())
        if (range == Range.THIS_AND_FUTURE) {
            val member = authentication.asWalletMember()
            marker.andAppend("member", member.accountId.value)

            val ignoreResult = recurrenceService.ignore(
                authentication.toWalletId(),
                member,
                BillId(billId),
                authentication.getActionSource(),
            )
            marker.andAppend("ignoreResult", ignoreResult)

            return ignoreResult.map<HttpResponse<*>> {
                LOG.info(marker, "IgnoreBill")
                HttpResponse.ok<ResponseTO>()
            }.getOrElse { ignoreRecurrenceError ->
                val logMessage = "IgnoreBill"

                when (ignoreRecurrenceError) {
                    IgnoreRecurrenceErrors.BillNotFound -> {
                        LOG.warn(
                            marker.andAppend("httpStatus", HttpStatus.NOT_FOUND.code),
                            logMessage,
                        )
                        StandardHttpResponses.billNotFound()
                    }

                    IgnoreRecurrenceErrors.BillIsNotIgnorable, IgnoreRecurrenceErrors.BillIsNotRecurring -> {
                        LOG.warn(marker.andAppend("httpStatus", HttpStatus.CONFLICT.code), logMessage)
                        HttpResponse.status<ResponseTO>(HttpStatus.CONFLICT)
                            .body(ResponseTO("409", "Bill not ignorable"))
                    }

                    is IgnoreRecurrenceErrors.ServerException -> {
                        LOG.error(marker, logMessage, ignoreRecurrenceError.exception)
                        StandardHttpResponses.serverError()
                    }

                    is IgnoreRecurrenceErrors.MemberNotAllowed -> {
                        LOG.warn(
                            marker.andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                            logMessage,
                        )
                        HttpResponse.status<ResponseTO>(HttpStatus.FORBIDDEN)
                    }
                }
            }
        }

        val member = authentication.asWalletMember()
        val ignoreBillResult = updateBillService.ignoreBill(
            billId = BillId(billId),
            walletId = authentication.toWalletId(),
            member = member,
            actionSource = authentication.getActionSource(),
        ).map<HttpResponse<*>> {
            HttpResponse.ok<ResponseTO>()
        }

        return ignoreBillResult.getOrElse { err ->
            when (err) {
                is BillAlreadyLockedException -> HttpResponse.status<ResponseTO>(HttpStatus.LOCKED)
                    .body(ResponseTO("423", err.message!!))

                is ItemNotFoundException -> StandardHttpResponses.billNotFound()
                is MemberNotAllowedException -> {
                    LOG.warn(
                        append("accountId", authentication.name)
                            .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                            .and<LogstashMarker>(append("billId", billId))
                            .and<LogstashMarker>(append("httpStatus", HttpStatus.FORBIDDEN.code)),
                        "IgnoreBill",
                    )
                    HttpResponse.status(HttpStatus.FORBIDDEN)
                }

                is BillNotIgnorableException -> {
                    LOG.warn(
                        append("accountId", authentication.name)
                            .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                            .and<LogstashMarker>(append("billId", billId))
                            .and<LogstashMarker>(append("httpStatus", HttpStatus.CONFLICT.code))
                            .and(append("error", err.message)),
                        "IgnoreBill",
                    )
                    HttpResponse.status<ResponseTO>(HttpStatus.CONFLICT).body(ResponseTO("409", "Bill not ignorable"))
                }

                else -> {
                    LOG.error(
                        append("accountId", authentication.name)
                            .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                            .and<LogstashMarker>(append("billId", billId))
                            .and<LogstashMarker>(append("httpStatus", HttpStatus.CONFLICT.code))
                            .and(append("error", err.message)),
                        "IgnoreBill",
                        err,
                    )
                    HttpResponse.status<Unit>(HttpStatus.INTERNAL_SERVER_ERROR)
                }
            }
        }
    }

    @Put("/id/{billId}/reactivate")
    fun reactivateBill(@PathVariable billId: String, authentication: Authentication): HttpResponse<*> {
        val member = authentication.asWalletMember()

        if (isBoletoInMaintenanceMode(authentication)) {
            return HttpResponse.status<ResponseTO>(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ResponseTO("5030", "Serviço em manutenção"))
        }

        return updateBillService.reactivateBill(
            billId = BillId(billId),
            walletId = authentication.toWalletId(),
            member = member,
            actionSource = authentication.getActionSource(),
        ).map<HttpResponse<*>> {
            StandardHttpResponses.ok(createPixHelper.mapFrom(it, member))
        }.getOrElse {
            when (it) {
                is ItemNotFoundException -> StandardHttpResponses.billNotFound()
                is NewerBillExistsException -> StandardHttpResponses.conflict(ResponseTO("4050", it.message.orEmpty()))
                is MemberNotAllowedException -> {
                    LOG.warn(
                        append("accountId", authentication.name)
                            .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                            .and<LogstashMarker>(append("billId", billId))
                            .and<LogstashMarker>(append("httpStatus", HttpStatus.FORBIDDEN.code)),
                        "ReactivateBill",
                    )
                    HttpResponse.status(HttpStatus.FORBIDDEN)
                }

                else -> {
                    LOG.error(
                        append("accountId", authentication.name)
                            .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                            .and<LogstashMarker>(append("billId", billId))
                            .and<LogstashMarker>(append("httpStatus", HttpStatus.BAD_REQUEST.code))
                            .and(append("error", it.message)),
                        "ReactivateBill",
                    )
                    HttpResponse.status<ResponseTO>(HttpStatus.BAD_REQUEST)
                        .body(ResponseTO("400", "Bill can not be reactivated"))
                }
            }
        }
    }

    @Put("/id/{billId}/mark-as-paid")
    fun markAsPaid(@PathVariable billId: String, authentication: Authentication): HttpResponse<*> {
        val member = authentication.asWalletMember()
        return updateBillService.markAsPaid(
            billId = BillId(billId),
            walletId = authentication.toWalletId(),
            member = member,
            amountPaid = null,
            actionSource = authentication.getActionSource(),
        ).map<HttpResponse<*>> {
            StandardHttpResponses.ok(createPixHelper.mapFrom(it, member))
        }.getOrElse { err ->
            val marker = append("accountId", authentication.name)
                .andAppend("walletId", authentication.getWallet().id.value)
                .andAppend("billId", billId)
            when (err) {
                is BillAlreadyLockedException -> HttpResponse.status<ResponseTO>(HttpStatus.LOCKED)
                    .body(ResponseTO("423", err.message!!))

                is MemberNotAllowedException -> {
                    LOG.warn(
                        marker.andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                        "MarkAsPaid",
                    )
                    HttpResponse.status(HttpStatus.FORBIDDEN)
                }

                is InvalidBillStateChangeException -> {
                    LOG.info(
                        marker.andAppend("httpStatus", HttpStatus.CONFLICT.code),
                        "MarkAsPaid",
                    )
                    HttpResponse.status(HttpStatus.CONFLICT)
                }

                else -> {
                    LOG.error(
                        marker.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code)
                            .andAppend("error", err.message),
                        "MarkAsPaid",
                    )
                    HttpResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
                }
            }
        }
    }

    @Get("/id/{billId}")
    fun getBillById(
        @PathVariable billId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val member = authentication.asWalletMember()
        val walletId = authentication.getWallet().id

        val markers = append("accountId", authentication.name)
            .andAppend("walletId", walletId.value)
            .andAppend("billId", billId)
        LOG.info(markers, "BillController#GetBillById")

        val billView = try {
            findBillService.find(billId = BillId(billId), walletId = walletId)
        } catch (e: IllegalStateException) {
            return StandardHttpResponses.billNotFound()
        }

        val billTO = billTOBuilder.toBillTO(billView, member)

        LOG.info(markers, "BillController#GetBillById")
        return HttpResponse.ok(billTO)
    }

    @Put("/id/{billId}/cancel-marked-as-paid")
    fun cancelMarkAsPaid(@PathVariable billId: String, authentication: Authentication): HttpResponse<*> {
        val member = authentication.asWalletMember()

        if (isBoletoInMaintenanceMode(authentication)) {
            return HttpResponse.status<ResponseTO>(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ResponseTO("5030", "Serviço em manutenção"))
        }

        return updateBillService.cancelMarkAsPaid(
            billId = BillId(billId),
            walletId = authentication.toWalletId(),
            member = member,
            actionSource = authentication.getActionSource(),
        ).map<HttpResponse<*>> {
            StandardHttpResponses.ok(createPixHelper.mapFrom(it, member))
        }.getOrElse { err ->
            val marker = append("accountId", authentication.name)
                .andAppend("walletId", authentication.getWallet().id.value)
                .andAppend("billId", billId)
            when (err) {
                is BillAlreadyLockedException -> HttpResponse.status<ResponseTO>(HttpStatus.LOCKED)
                    .body(ResponseTO("423", err.message!!))

                is MemberNotAllowedException -> {
                    LOG.warn(
                        marker.andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                        "CancelMarkAsPaid",
                    )
                    HttpResponse.status(HttpStatus.FORBIDDEN)
                }

                is InvalidBillStateChangeException -> {
                    LOG.info(
                        marker.andAppend("httpStatus", HttpStatus.CONFLICT.code),
                        "CancelMarkAsPaid",
                    )
                    HttpResponse.status(HttpStatus.CONFLICT)
                }

                else -> {
                    LOG.error(
                        marker.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code)
                            .andAppend("error", err.message),
                        "CancelMarkAsPaid",
                    )
                    HttpResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
                }
            }
        }
    }

    @Put("/id/{billId}/approve")
    fun approve(@PathVariable billId: String, @Body body: ApproveBillTO, authentication: Authentication): HttpResponse<Any?> {
        val markers = log("billId" to billId, "wallet_id" to authentication.toWalletId().value)

        val bill = updateBillService.approve(
            BillId(billId),
            authentication.toWalletId(),
            authentication.asWalletMember(),
            body.alwaysAllowSender ?: false,
            authentication.getActionSource(),
        ).getOrElse {
            val (status, body) = when (it.ex) {
                is BillAlreadyLockedException -> HttpStatus.LOCKED to ResponseTO("423", it.message)
                is MemberNotAllowedException -> HttpStatus.FORBIDDEN to null
                else -> HttpStatus.INTERNAL_SERVER_ERROR to null
            }
            LOG.error(markers.and("http_status" to status.code), "BillController#approve", it.ex)

            return HttpResponse.status<Any?>(status).body(body)
        }

        LOG.info(markers, "BillController#approve")

        return HttpResponse.ok(bill)
    }

    @Put("/id/{billId}/deny")
    fun deny(@PathVariable billId: String, authentication: Authentication): HttpResponse<Any> {
        val markers = log("billId" to billId, "wallet_id" to authentication.toWalletId().value)

        val bill = updateBillService.deny(
            BillId(billId),
            authentication.toWalletId(),
            authentication.asWalletMember(),
            authentication.getActionSource(),
        ).getOrElse {
            val (status, body) = when (it.ex) {
                is BillAlreadyLockedException -> HttpStatus.LOCKED to ResponseTO("423", it.message)
                is MemberNotAllowedException -> HttpStatus.FORBIDDEN to null
                else -> HttpStatus.INTERNAL_SERVER_ERROR to null
            }

            LOG.error(markers.and("http_status" to status.code), "BillController#deny", it.ex)

            return HttpResponse.status<Any?>(status).body(body)
        }

        LOG.info(markers, "BillController#deny")

        return HttpResponse.ok(bill)
    }

    @Post("/invoice{?dryRun}")
    fun postInvoice(
        @Body @Valid
        createInvoiceTO: CreateInvoiceTO,
        @QueryValue(defaultValue = "false") dryRun: Boolean,
        authentication: Authentication,
    ): HttpResponse<*> {
        if (!validDocument(createInvoiceTO.recipient)) {
            LOG.warn(
                append("accountId", authentication.toAccountId())
                    .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                    .and<LogstashMarker>(append("request", createInvoiceTO))
                    .and(append("dryRun", dryRun)),
                "AddInvoice",
            )
            return HttpResponse.badRequest(ResponseTO("400", "Document does not match required pattern"))
        }
        if (createInvoiceTO.recipient.bankDetails.bankNo == null) {
            return HttpResponse.badRequest(ResponseTO("400", "BankNo can't be null"))
        }

        val recipient = buildRecipient(createInvoiceTO, authentication.toContactAccountId())

        return createInvoiceTO.recurrence?.let {
            tryCreateRecurrence(authentication, createInvoiceTO, recipient, dryRun)
        } ?: tryCreateInvoice(authentication, createInvoiceTO, recipient, dryRun)
    }

    @Post("/pix{?dryRun}")
    fun postPix(
        @Body @Valid
        createPixTO: CreatePixTO,
        @QueryValue(defaultValue = "false") dryRun: Boolean,
        authentication: Authentication,
    ): HttpResponse<*> {
        val recipient = createPixHelper.buildRecipient(createPixTO, authentication.toContactAccountId())
        val request = createPixHelper.buildCreatePixRequest(authentication, createPixTO, recipient)

        val marker = append("accountId", authentication.toAccountId())
            .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
            .and<LogstashMarker>(append("request", createPixTO))

        if (!request.recipient.checkExistsOneRecipient()) {
            return StandardHttpResponses.badRequest(
                CreateBillError.PIX_MUST_HAVE_BANK_ACCOUNT_OR_PIX_KEY_OR_QRCODE.code,
                CreateBillError.PIX_MUST_HAVE_BANK_ACCOUNT_OR_PIX_KEY_OR_QRCODE.description,
            )
        }

        if (request.recipient.qrCode != null && createPixTO.recurrence != null) {
            return StandardHttpResponses.badRequest(
                CreateBillError.PIX_QR_CODE_CANT_BE_RECURRENT.code,
                CreateBillError.PIX_QR_CODE_CANT_BE_RECURRENT.description,
            )
        }

        if (request.recipient.pixKey != null && !request.recipient.pixKey.isValid()) {
            LOG.warn(marker.and(append("error", CreateBillError.PIX_KEY_INVALID)), "AddPix")
            return StandardHttpResponses.badRequest(
                CreateBillError.PIX_KEY_INVALID.code,
                CreateBillError.PIX_KEY_INVALID.description + request.recipient.pixKey.type,
            )
        }

        return createPixTO.recurrence?.let {
            tryCreateRecurrence(authentication, createPixTO, recipient, dryRun)
        } ?: createPixHelper.tryCreatePix(request, authentication.asWalletMember(), dryRun, marker)
    }

    @Error
    fun handleConstraintViolationException(
        request: HttpRequest<*>,
        exception: ConstraintViolationException,
    ): HttpResponse<ResponseTO> {
        val marker = empty()
        if (request.body.isPresent) {
            marker.add(append("request", request.body.get()))
        }
        LOG.warn(marker, "BillControllerConstraintViolation", exception)
        return HttpResponse.badRequest(
            ResponseTO(
                message = exception.constraintViolations.first().message,
                code = "4009",
            ),
        )
    }

    private fun tryCreateRecurrence(
        authentication: Authentication,
        createPixTO: CreatePixTO,
        recipient: RecipientRequest,
        dryRun: Boolean,
    ): HttpResponse<*> {
        return try {
            val recurrenceResult =
                recurrenceService.create(buildRecurrence(authentication, createPixTO, recipient), dryRun)
            handleRecurrenceResult(recurrenceResult, authentication, createPixTO, dryRun)
        } catch (exception: DateTimeParseException) {
            StandardHttpResponses.badRequest("4000", "Invalid duedate: ${createPixTO.dueDate}")
        }
    }

    private fun tryCreateRecurrence(
        authentication: Authentication,
        createInvoiceTO: CreateInvoiceTO,
        recipient: RecipientRequest,
        dryRun: Boolean,
    ): HttpResponse<*> {
        return try {
            val response = recurrenceService.create(buildRecurrence(authentication, createInvoiceTO, recipient), dryRun)
            handleRecurrenceResult(response, authentication, createInvoiceTO, dryRun)
        } catch (exception: DateTimeParseException) {
            StandardHttpResponses.badRequest("4000", "Invalid duedate: ${createInvoiceTO.dueDate}")
        }
    }

    private fun handleRecurrenceResult(
        result: Either<RecurrenceCreationError, RecurrenceResult<BillRecurrence>>,
        authentication: Authentication,
        request: Any,
        dryRun: Boolean,
    ): HttpResponse<*> {
        return result.fold(
            ifLeft = {
                handleRecurrenceCreationError(
                    it,
                    append("accountId", authentication.toAccountId())
                        .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                        .and<LogstashMarker>(append("request", request))
                        .and(append("dryRun", dryRun)),
                )
            },
            ifRight = { recurrenceResult ->
                return if (recurrenceResult.recurrence.recipientPixKey != null) {
                    val account = accountService.findAccountById(authentication.toAccountId())
                    val pixKeyDetails =
                        retrievePixKeyDetails(
                            pixKey = recurrenceResult.recurrence.recipientPixKey,
                            document = account.document,
                        )
                    StandardHttpResponses.created(
                        mapFrom(
                            recurrence = recurrenceResult.recurrence,
                            pixKeyDetails = pixKeyDetails,
                            warningCode = recurrenceResult.warningCode,
                            possibleDuplicateBills = recurrenceResult.possibleDuplicateBills,
                        ),
                    )
                } else {
                    StandardHttpResponses.created(
                        mapFrom(
                            recurrence = recurrenceResult.recurrence,
                            pixKeyDetails = null,
                            warningCode = recurrenceResult.warningCode,
                            possibleDuplicateBills = recurrenceResult.possibleDuplicateBills,
                        ),
                    )
                }
            },
        )
    }

    private fun retrievePixKeyDetails(pixKey: PixKey, document: String): PixKeyDetails? {
        return pixKeyManagement.findKeyDetailsCacheable(
            key = pixKey,
            document = document,
        ).pixKeyDetails
    }

    private fun handleRecurrenceCreationError(
        error: RecurrenceCreationError,
        markers: LogstashMarker,
    ): HttpResponse<*> {
        return when (error) {
            RecurrenceCreationError.DueDateAfterLimit,
            RecurrenceCreationError.DueDateInThePast,
            -> StandardHttpResponses.badRequest("4000", error.message)

            is RecurrenceCreationError.ServerError -> {
                LOG.error(markers, "AddInvoice", error.exception)
                StandardHttpResponses.serverError("Failed to create recurrence")
            }
        }
    }

    private fun tryCreateInvoice(
        authentication: Authentication,
        createInvoiceTO: CreateInvoiceTO,
        recipient: RecipientRequest,
        dryRun: Boolean,
    ): HttpResponse<*> {
        val createInvoiceRequest = buildCreateInvoiceRequest(authentication, createInvoiceTO, recipient)
        return when (val addInvoiceResult = createBillService.createInvoice(createInvoiceRequest, dryRun)) {
            is CreateBillResult.SUCCESS -> StandardHttpResponses.created(
                createPixHelper.mapFrom(
                    addInvoiceResult.bill,
                    authentication.asWalletMember(),
                    addInvoiceResult.warningCode,
                    addInvoiceResult.possibleDuplicateBills,
                ),
            )

            is CreateBillResult.FAILURE.ServerError -> {
                LOG.error(
                    append("accountId", authentication.toAccountId())
                        .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                        .and<LogstashMarker>(append("request", createInvoiceTO))
                        .and<LogstashMarker>(append("AddInvoiceResult", addInvoiceResult))
                        .and(append("dryRun", dryRun)),
                    "AddInvoice",
                    addInvoiceResult.throwable,
                )
                StandardHttpResponses.serverError("Error to add invoice")
            }

            else -> {
                LOG.error(
                    append("accountId", authentication.toAccountId())
                        .and<LogstashMarker>(append("walletId", authentication.getWallet().id.value))
                        .and<LogstashMarker>(append("request", createInvoiceTO))
                        .and<LogstashMarker>(append("AddInvoiceResult", addInvoiceResult))
                        .and(append("dryRun", dryRun)),
                    "AddInvoice",
                )
                StandardHttpResponses.serverError("Error to add invoice")
            }
        }
    }

    private fun buildRecurrence(
        authentication: Authentication,
        createInvoiceTO: CreateInvoiceTO,
        recipient: RecipientRequest,
    ) =
        BillRecurrence(
            walletId = authentication.toWalletId(),
            description = createInvoiceTO.description,
            amount = createInvoiceTO.amount,
            rule = RecurrenceRule(
                frequency = createInvoiceTO.recurrence!!.frequency,
                startDate = LocalDate.parse(createInvoiceTO.dueDate, dateFormat),
                pattern = createInvoiceTO.recurrence.pattern.orEmpty(),
                endDate = createInvoiceTO.recurrence.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
            ),
            contactId = createInvoiceTO.recipient.id?.let { ContactId(it) },
            contactAccountId = authentication.toContactAccountId(),
            recipientName = recipient.name.orEmpty(),
            recipientDocument = recipient.document,
            recipientAlias = recipient.alias,
            recipientBankAccount = recipient.bankAccount,
            recipientPixKey = recipient.pixKey,
            actionSource = authentication.getActionSource(),
            created = getZonedDateTime(),
            status = RecurrenceStatus.ACTIVE,
            billType = BillType.INVOICE,
        )

    private fun buildRecurrence(
        authentication: Authentication,
        createPixTO: CreatePixTO,
        recipient: RecipientRequest,
    ) =
        BillRecurrence(
            walletId = authentication.toWalletId(),
            description = createPixTO.description,
            amount = createPixTO.amount,
            rule = RecurrenceRule(
                frequency = createPixTO.recurrence!!.frequency,
                startDate = LocalDate.parse(
                    createPixTO.dueDate,
                    dateFormat,
                ),
                pattern = createPixTO.recurrence.pattern.orEmpty(),
                endDate = createPixTO.recurrence.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
            ),
            contactId = createPixTO.recipient.id?.let { ContactId(it) },
            contactAccountId = authentication.toContactAccountId(),
            recipientName = recipient.name.orEmpty(),
            recipientDocument = recipient.document,
            recipientAlias = recipient.alias,
            recipientBankAccount = recipient.bankAccount,
            recipientPixKey = recipient.pixKey,
            actionSource = authentication.getActionSource(),
            created = getZonedDateTime(),
            status = RecurrenceStatus.ACTIVE,
            billType = BillType.PIX,
            billCategoryId = createPixTO.categoryId?.let { PFMCategoryId(it) },
        )

    private fun buildCreateInvoiceRequest(
        authentication: Authentication,
        createInvoiceTO: CreateInvoiceTO,
        recipient: RecipientRequest,
    ) =
        CreateInvoiceRequest(
            description = createInvoiceTO.description,
            dueDate = LocalDate.parse(createInvoiceTO.dueDate, dateFormat),
            amount = createInvoiceTO.amount,
            recipient = recipient,
            contactId = createInvoiceTO.recipient.id?.let { ContactId(it) },
            source = authentication.getActionSource(),
            walletId = authentication.toWalletId(),
        )

    private fun buildRecipient(createInvoiceTO: CreateInvoiceTO, contactAccountId: AccountId) =
        with(createInvoiceTO.recipient) {
            RecipientRequest(
                id = id?.let { ContactId(it) },
                accountId = contactAccountId,
                document = document,
                name = name,
                alias = alias ?: "",
                bankAccount = BankAccount(
                    bankNo = bankDetails.bankNo,
                    accountType = bankDetails.accountType,
                    routingNo = bankDetails.routingNo,
                    accountNo = bankDetails.accountNo,
                    accountDv = bankDetails.accountDv,
                    document = document,
                    ispb = bankDetails.ispb,
                ),
                qrCode = null,
            )
        }

    // FIXME create annotation for validation
    private fun validDocument(invoiceRecipientTO: RequestInvoiceRecipientTO): Boolean =
        (invoiceRecipientTO.documentType == "CPF" && invoiceRecipientTO.document.matches(Regex("^\\d{11}$"))) ||
            (invoiceRecipientTO.documentType == "CNPJ" && invoiceRecipientTO.document.matches(Regex("^\\d{14}$")))

    private fun buildBoletoResponse(
        boletoResult: CreateBillResult,
        member: Member,
        dryRun: Boolean,
        markers: LogstashMarker,
    ): HttpResponse<*> {
        return when (boletoResult) {
            is CreateBillResult.SUCCESS -> {
                if (dryRun) {
                    return StandardHttpResponses.ok(
                        createPixHelper.mapFrom(
                            boletoResult.bill,
                            member,
                            boletoResult.warningCode,
                            boletoResult.possibleDuplicateBills,
                        ),
                    )
                }
                StandardHttpResponses.created(
                    createPixHelper.mapFrom(
                        boletoResult.bill,
                        member,
                        boletoResult.warningCode,
                        boletoResult.possibleDuplicateBills,
                    ),
                )
            }

            is CreateBillResult.FAILURE.BillAlreadyExists -> StandardHttpResponses.badRequest(
                BILL_ALREADY_INCLUDED.code,
                BILL_ALREADY_INCLUDED.description,
                createPixHelper.mapFrom(boletoResult.bill, member),
            )

            is CreateBillResult.FAILURE.BillNotPayable -> StandardHttpResponses.badRequest(
                boletoResult.code,
                boletoResult.description,
            )

            is CreateBillResult.FAILURE.AlreadyPaid.WithData, is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> StandardHttpResponses.badRequest(
                ALREADY_PAID.code,
                ALREADY_PAID.description,
            )

            is CreateBillResult.FAILURE.BillUnableToValidate -> StandardHttpResponses.customStatusResponse(
                HttpStatus.UNPROCESSABLE_ENTITY,
                ResponseTO(
                    code = AddBillError.UNABLE_TO_VALIDATE.code,
                    message = AddBillError.UNABLE_TO_VALIDATE.description,
                ),
            )

            is CreateBillResult.FAILURE.ServerError -> {
                LOG.error(markers, "buildBoletoResponse", boletoResult.throwable)
                StandardHttpResponses.serverError()
            }
        }
    }

    private fun mapFrom(
        recurrence: BillRecurrence,
        pixKeyDetails: PixKeyDetails?,
        warningCode: WarningCode? = null,
        possibleDuplicateBills: List<PossibleDuplicate>,
    ): BillTO {
        val source = when (recurrence.actionSource) {
            is ActionSource.Api -> ActionSource.WalletRecurrence(
                recurrenceId = recurrence.id,
                accountId = recurrence.actionSource.accountId,
            )

            // FIXME criar teste para este caso
            is ActionSource.Subscription -> ActionSource.WalletRecurrence(
                recurrenceId = recurrence.id,
                accountId = recurrence.actionSource.accountId,
            )

            else -> throw IllegalStateException("Recurrence's ActionSource should be Api or Subscription")
        }

        return BillTO(
            id = recurrence.bills.firstOrNull()?.value ?: FAKE_RECURRENCE_BILL_ID,
            description = recurrence.description,
            amount = recurrence.amount,
            amountTotal = recurrence.amount,
            interest = 0,
            fine = 0,
            discount = 0,
            paymentLimitTime = tedConfiguration.limitTime,
            dueDate = recurrence.rule.startDate.format(dateFormat),
            status = BillStatus.ACTIVE.name,
            createdOn = recurrence.created.format(dateTimeFormat),
            billType = recurrence.billType.name,
            recipient = convertToRecipientTo(recurrence, pixKeyDetails),
            billRecipient = convertToRecipientTo(recurrence, pixKeyDetails),
            source = source.toBillSourceTO(),
            warningCode = warningCode?.name ?: WarningCode.NONE.name,
            recurrence = RecurrenceResponseTO(
                id = recurrence.id.value,
                frequency = recurrence.rule.frequency.name,
                startDate = recurrence.rule.startDate.format(dateFormat),
                pattern = recurrence.rule.pattern,
                endDate = recurrence.rule.endDate?.format(dateFormat),
                occurrence = recurrence.rule.calculateOccurrence(recurrence.rule.startDate),
            ),
            effectiveDueDate = recurrence.rule.startDate.format(dateFormat),
            possibleDuplicateBills = possibleDuplicateBills.map {
                PossibleDuplicateBillTO(
                    billId = it.billId.value,
                    dueDate = it.dueDate.format(
                        dateFormat,
                    ),
                )
            },
            availablePaymentMethods = listOf(PaymentMethodType.BALANCE), // TODO - isso não deveria ser criado aqui
            tags = emptySet(),
            pixQrCodeData = null,
            categorySuggestions = emptyList(),
            goalId = null,
            imageUrl = null,
            modulesFields = emptyMap(),
        )
    }

    private fun convertToRecipientTo(recurrence: BillRecurrence, pixKeyDetails: PixKeyDetails?): ResponseRecipientTO {
        return pixKeyDetails?.let {
            ResponseRecipientTO(
                document = pixKeyDetails.owner.document,
                alias = recurrence.recipientAlias,
                name = pixKeyDetails.owner.name,
                bankDetails = billTOBuilder.convertToBankDetailsTO(recurrence.recipientBankAccount),
                pixKey = PixKeyResponseTO(
                    type = pixKeyDetails.key.type,
                    value = pixKeyDetails.key.value,
                    routingNo = pixKeyDetails.holder.routingNo,
                    accountNo = pixKeyDetails.holder.accountNo,
                    accountDv = pixKeyDetails.holder.accountDv,
                    ispb = pixKeyDetails.holder.ispb,
                    institutionName = pixKeyDetails.holder.institutionName,
                    document = pixKeyDetails.owner.document,
                    accountType = pixKeyDetails.holder.accountType,
                    name = pixKeyDetails.owner.name,
                ),
                pixQrCodeData = null,
            )
        } ?: ResponseRecipientTO(
            document = recurrence.recipientDocument,
            alias = recurrence.recipientAlias,
            name = recurrence.recipientName,
            bankDetails = billTOBuilder.convertToBankDetailsTO(recurrence.recipientBankAccount),
            pixKey = null,
            pixQrCodeData = null,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BillController::class.java)
    }
}

data class ApproveBillTO(
    val alwaysAllowSender: Boolean?,
)

fun RecurrenceRule.calculateOccurrence(dueDate: LocalDate): Int {
    var count = 1L
    while (this.nextDate(currentDate = this.startDate, count = count) <= dueDate) {
        count += 1
    }
    return count.toInt()
}

enum class ActionSourceType {
    DDA, Webapp, MailBox, Recurrence, WalletRecurrence, Api, WalletMailBox, DirectDebit, ConcessionariaDireto, OnePixPay, ConnectUtility, OpenFinance, VirtualAssistant, Unknown, GoalInvestment, VehicleDebts, AutomaticPix
}

fun BillPayer?.toPayerTO(): PayerTO? = this?.let { PayerTO(name = name, document = document, alias = alias) }

@Introspected
data class AddConcessionariaTO(
    @field:Size(min = 48, max = 48, message = "Digitable line should be 48 digits") val digitableLine: String,
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
    @field:Pattern(
        regexp = "^\\d{4}-\\d{2}-\\d{2}$",
        message = "Due date should be format yyyy-MM-dd",
    ) val dueDate: String,
)

@Introspected
data class AddFichaDeCompensacaoTO(
    @field:Size(min = 47, max = 47, message = "Digitable line should be 47 digits") val digitableLine: String,
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
)

@Introspected
data class UpdateDescriptionTO(
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
)

@Introspected
data class UpdateAmountTO(
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    val range: Range? = Range.THIS,
)

@Introspected
data class PutBillStatusTo(val status: BillStatus)

@Introspected
data class CreateInvoiceTO(
    @field:Valid val recipient: RequestInvoiceRecipientTO,
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    @field:Pattern(
        regexp = "^\\d{4}-\\d{2}-\\d{2}$",
        message = "Due date should be format yyyy-MM-dd",
    ) val dueDate: String,
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
    @field:Valid val recurrence: RecurrenceRequestTO? = null,
)

@Introspected
data class CreatePixTO(
    @field:Valid val recipient: RequestPixRecipientTO,
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
    @field:Pattern(
        regexp = "^\\d{4}-\\d{2}-\\d{2}$",
        message = "Due date should be format yyyy-MM-dd",
    ) val dueDate: String,
    @field:Valid val recurrence: RecurrenceRequestTO? = null,
    val categoryId: String? = null,
    val automaticPixMaximumAmount: Long? = null,
)

@Introspected
data class RecurrenceRequestTO(
    val frequency: RecurrenceFrequency,
    @field:Pattern(
        regexp = "^\\d{4}-\\d{2}-\\d{2}$",
        message = "End date should be format yyyy-MM-dd",
    ) val endDate: String? = null,
    val pattern: String? = null,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class RecurrenceResponseTO(
    val id: String,
    val startDate: String,
    val frequency: String,
    val endDate: String? = null,
    val pattern: String? = null,
    val occurrence: Int,
)

@Introspected
data class RequestInvoiceRecipientTO(
    val id: String? = null,
    val name: String,
    @field:Pattern(
        regexp = "^\\d{11}|\\d{14}$",
        message = "document must be 11 digits for CPF or 14 digits for CNPJ",
    ) val document: String,
    @field:Pattern(regexp = "CPF|CNPJ", message = "Document type must be CPF or CNPJ") val documentType: String,
    @field:Valid val bankDetails: RecipientBankDetailsTO,
    @field:Nullable
    @field:Size(max = 60, message = "Alias length must be up to 60")
    val alias: String? = "",
)

@Introspected
data class RequestPixRecipientTO(
    val id: String? = null,
    val name: String?,
    @field:Pattern(
        regexp = "^\\d{11}|\\d{14}$",
        message = "document must be 11 digits for CPF or 14 digits for CNPJ",
    ) val document: String?,
    @field:Pattern(regexp = "CPF|CNPJ", message = "Document type must be CPF or CNPJ") val documentType: String?,
    @field:Valid val bankDetails: RecipientBankDetailsTO?,
    @field:Nullable
    @field:Size(max = 60, message = "Alias length must be up to 60")
    val alias: String? = "",
    val pixKey: PixKeyRequestTO? = null,
    val qrCode: String? = null,
)

data class PixKeyRequestTO(
    val value: String,
    val type: PixKeyType,
)

@Introspected
data class RecipientBankDetailsTO(
    val accountType: AccountType,
    @field:Positive(message = "Invalid Bank Number") val bankNo: Long? = null,
    @field:Positive(message = "Invalid Routing Number")
    @field:Digits(
        integer = 4,
        fraction = 0,
        message = "Routing number should be 4 digits",
    )
    val routingNo: Long,
    @field:Positive(message = "Invalid Account Number") val accountNo: BigInteger,
    @field:Pattern(
        regexp = "^[\\dxX]$",
        message = "Account check digits should be a digit or the X char",
    ) val accountDv: String,
    @field:Pattern(regexp = "^\\d{8}$", message = "ispb should be 8 digits") val ispb: String? = null,
)

fun RecipientBankDetailsTO.toBankAccount() = BankAccount(
    accountType = accountType,
    bankNo = bankNo,
    routingNo = routingNo,
    accountNo = accountNo,
    accountDv = accountDv,
    ispb = ispb,
)

data class PayerTO(
    val name: String?,
    val document: String,
    val alias: String?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class BillTO(
    val id: String,
    val description: String = "",
    override val amount: Long,
    val amountTotal: Long,
    val interest: Long = 0,
    val fine: Long = 0,
    val discount: Long = 0,
    val paymentLimitTime: String,
    override val dueDate: String,
    val status: String,
    val createdOn: String,
    val billType: String,
    val recipient: ResponseRecipientTO?,
    val billRecipient: ResponseRecipientTO?,
    val barcode: String? = null,
    val paidDate: String? = null,
    val assignor: String? = null,
    var overdue: Boolean? = null,
    val source: BillSourceTO,
    val payer: PayerTO? = null,
    val warningCode: String,
    val amountCalculationModel: String? = null,
    val recurrence: RecurrenceResponseTO? = null,
    val schedule: BillScheduleTO? = null,
    val externalId: ExternalBillId? = null,
    val effectiveDueDate: String,
    val fichaCompensacaoType: FichaCompensacaoType? = null,
    val amountPaid: Long? = null,
    val possibleDuplicateBills: List<PossibleDuplicateBillTO>,
    val markedAsPaidBy: String? = null,
    val subscriptionFee: Boolean = false,
    @Deprecated("use subscriptionFee") val fridaySubscription: Boolean = false,
    val trackingOutdated: Boolean = false,
    val securityValidationResult: String? = null,
    val availablePaymentMethods: List<PaymentMethodType>,
    val transactionCorrelationId: String? = null,
    val batchSchedulingId: String? = null,
    val internalSettlement: Boolean = true,
    val paymentDetails: PaymentDetailsTO? = null,
    val tags: Set<BillTag>,
    val category: BillCategoryTO? = null,
    val categorySuggestions: List<BillCategoryTO?>,
    val pixQrCodeData: PixQrCodeDataTO?,
    val brand: String? = null,
    val participants: BillParticipantsTO? = null,
    val goalId: String?,
    val imageUrl: String?,
    val allowedActions: Set<String> = emptySet(),
    private val modulesFields: Map<String, ModuleFieldsTO> = emptyMap(),
) : TimelineExpenseEntryTO {
    override val timelineEntryType = "BILL"

    @JsonAnyGetter
    fun getModulesFields(): Map<String, ModuleFieldsTO> = modulesFields
}

object BaseKnownBillActions {
    const val IGNORE_BILL = "IGNORE_BILL"
}

interface ModuleFieldsTO

data class BillScheduleTO(
    val date: String,
    val waitingFunds: Boolean,
    val waitingRetry: Boolean,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class BillParticipantsTO(
    val owner: BillParticipantTO,
    val scheduledBy: BillParticipantTO?,
    val paidBy: BillParticipantTO?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class BillParticipantTO(
    val walletId: String,
    val name: String,
    val imageUrlSmall: String?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class BillSourceTO(
    val type: String,
    val role: String? = null,
    val email: String? = null,
    val accountId: String? = null,
    val externalId: String? = null,
    val bankCode: Long? = null,
    val goalId: String? = null,
    val licensePlate: String? = null,
    val endToEnd: String? = null,
    val recurringPaymentId: String? = null,
)

data class ResponseRecipientTO(
    val name: String?,
    val document: String?,
    val bankDetails: RecipientBankDetailsTO?,
    val alias: String? = "",
    val pixKey: PixKeyResponseTO?,
    val pixQrCodeData: PixQrCodeDataTO?,
)

data class PixKeyResponseTO(
    // FIXME nullable adicionado na recorrencia com chave - remover nulidade
    val type: PixKeyType,
    val value: String,
    val routingNo: Long? = null,
    val accountNo: BigInteger? = null,
    val accountDv: String? = null,
    val ispb: String? = null,
    val institutionName: String? = null,
    val document: String? = null,
    val accountType: AccountType? = null,
    val name: String? = null,
)

data class PossibleDuplicateBillTO(
    val billId: String,
    val dueDate: String? = null,
)

data class PaymentDetailsTO(
    val installments: Int,
    val installmentAmount: Long,
    val fee: Long,
    val feeAmount: Long,
)

data class BillCategoryTO(
    val billCategoryId: String,
    val icon: String,
    val name: String,
    val enabled: Boolean,
)

fun Authentication.getActionSource(): ActionSource.Api {
    return ActionSource.Api(this.toAccountId())
}

fun Authentication.toWalletId() = getWallet().id

const val FAKE_RECURRENCE_BILL_ID = "ACCOUNT-********-0000-0000-0000-********0000"

fun convertToLegacyStatusTO(status: BillStatus): String {
    // TODO: delete this as soon as possible.
    return if (status == WAITING_APPROVAL) {
        "PENDING"
    } else {
        status.name
    }
}

@Suppress("DEPRECATION")
fun ActionSource.toBillSourceTO(): BillSourceTO {
    fun parseFromMailBoxActionSource(from: String?): String? {
        if (from == null) {
            return null
        }

        if (from.contains("@")) {
            return from
        }

        return null
    }

    return when (this) {
        is ActionSource.DDA -> BillSourceTO(
            type = ActionSourceType.DDA.name,
            accountId = accountId.value,
        )

        is ActionSource.Webapp -> BillSourceTO(type = ActionSourceType.Webapp.name, role = role.name)
        is ActionSource.MailBox -> BillSourceTO(
            type = ActionSourceType.MailBox.name,
            email = parseFromMailBoxActionSource(from),
            role = role?.name,
        )

        is ActionSource.WalletMailBox -> BillSourceTO(
            type = ActionSourceType.MailBox.name,
            email = parseFromMailBoxActionSource(from),
            accountId = accountId?.value,
        )

        is ActionSource.Recurrence -> BillSourceTO(
            type = ActionSourceType.Recurrence.name,
            role = role.name,
        )

        is ActionSource.WalletRecurrence -> BillSourceTO(
            type = ActionSourceType.Recurrence.name,
            accountId = accountId.value,
        )

        is ActionSource.SubscriptionRecurrence -> BillSourceTO(
            type = ActionSourceType.Recurrence.name,
            accountId = accountId.value,
        )

        is ActionSource.Api -> BillSourceTO(
            type = ActionSourceType.Webapp.name,
            accountId = accountId.value,
        )

        is ActionSource.Subscription -> BillSourceTO(
            type = ActionSourceType.Webapp.name,
            accountId = accountId.value,
        )

        is ActionSource.DirectDebit -> BillSourceTO(
            type = ActionSourceType.DirectDebit.name,
            externalId = identification,
        )

        is ActionSource.OpenFinance -> BillSourceTO(
            type = ActionSourceType.OpenFinance.name,
            accountId = accountId.value,
            bankCode = bankNumber,
        )

        is ActionSource.System -> BillSourceTO(
            type = ActionSourceType.Unknown.name,
        )

        is ActionSource.ConnectUtility -> BillSourceTO(
            type = ActionSourceType.ConnectUtility.name,
        )

        is ActionSource.VirtualAssistant -> BillSourceTO(
            type = ActionSourceType.VirtualAssistant.name,
        )

        is ActionSource.GoalInvestment -> BillSourceTO(
            type = ActionSourceType.GoalInvestment.name,
            accountId = accountId.value,
            goalId = goalId.value,
        )

        is ActionSource.VehicleDebts -> BillSourceTO(
            type = ActionSourceType.VehicleDebts.name,
            accountId = accountId.value,
            licensePlate = licensePlate?.value,
        )

        ActionSource.Scheduled, ActionSource.OnePixPay -> throw IllegalStateException("Illegal action source: $this")
        is ActionSource.AutomaticPix -> BillSourceTO(
            type = ActionSourceType.AutomaticPix.name,
            accountId = accountId.value,
            endToEnd = endToEnd,
            recurringPaymentId = recurringPaymentId,
        )
    }
}

fun mapFichaCompensacaoType(
    fichaCompensacaoType: FichaCompensacaoType?,
    recipient: Recipient?,
): FichaCompensacaoType? {
    return fichaCompensacaoType?.let {
        when {
            isCreditCard(fichaCompensacaoType, recipient) -> FichaCompensacaoType.CARTAO_DE_CREDITO
            it == FichaCompensacaoType.BOLETO_PROPOSTA -> it
            else -> FichaCompensacaoType.OUTROS
        }
    }
}

class CreatePixHelper(
    private val createBillService: CreateBillService,
    private val billImageProvider: BillImageProvider,
    private val billTOBuilder: BillTOBuilder,
    private val log: Logger,
) {

    fun buildRecipient(createPixTO: CreatePixTO, contactAccountId: AccountId) =
        with(createPixTO.recipient) {
            RecipientRequest(
                id = id?.let { ContactId(it) },
                accountId = contactAccountId,
                document = document,
                name = name,
                alias = alias ?: "",
                bankAccount = bankDetails?.let {
                    BankAccount(
                        bankNo = it.bankNo,
                        ispb = it.ispb,
                        accountType = it.accountType,
                        routingNo = it.routingNo,
                        accountNo = it.accountNo,
                        accountDv = it.accountDv,
                        document = document
                            ?: throw IllegalArgumentException("pix with bank account should have document"),
                    )
                },
                pixKey = pixKey?.let {
                    PixKey(value = it.value.lowercase(), type = it.type)
                },
                qrCode = qrCode,
            )
        }

    fun buildCreatePixRequest(
        authentication: Authentication,
        createPixTO: CreatePixTO,
        recipient: RecipientRequest,
    ) =
        CreatePixRequest(
            description = createPixTO.description,
            dueDate = LocalDate.parse(createPixTO.dueDate, dateFormat),
            amount = createPixTO.amount,
            recipient = recipient,
            source = authentication.getActionSource(),
            walletId = authentication.toWalletId(),
            categoryId = createPixTO.categoryId?.let { PFMCategoryId(it) },
            automaticPixAuthorizationMaximumAmount = createPixTO.automaticPixMaximumAmount,
            automaticPixData = null,
        )

    fun buildCreateChatbotPixRequest(
        accountId: AccountId,
        createPixTO: CreatePixTO,
        recipient: RecipientRequest,
        walletId: WalletId,
        transactionId: String?,
    ) =
        CreatePixRequest(
            description = createPixTO.description,
            dueDate = LocalDate.parse(createPixTO.dueDate, dateFormat),
            amount = createPixTO.amount,
            recipient = recipient,
            source = ActionSource.VirtualAssistant(accountId = accountId, transactionId = transactionId),
            walletId = walletId,
            automaticPixAuthorizationMaximumAmount = createPixTO.automaticPixMaximumAmount,
            automaticPixData = null,
        )

    fun tryCreatePix(
        request: CreatePixRequest,
        member: Member,
        dryRun: Boolean,
        marker: LogstashMarker,
    ): HttpResponse<out Any> {
        return when (val addPixResult = createBillService.createPix(request, dryRun)) {
            is CreateBillResult.FAILURE.BillNotPayable -> {
                log.warn(marker.and(Markers.append("AddPixResult", addPixResult)), "AddPix")
                StandardHttpResponses.badRequest(addPixResult.code, addPixResult.description)
            }

            is CreateBillResult.SUCCESS -> StandardHttpResponses.created(
                mapFrom(
                    addPixResult.bill,
                    member,
                    addPixResult.warningCode,
                    addPixResult.possibleDuplicateBills,
                ),
            )

            is CreateBillResult.FAILURE.ServerError -> {
                log.error(marker.and(Markers.append("AddPixResult", addPixResult)), "AddPix", addPixResult.throwable)
                StandardHttpResponses.serverError("Error to add pix")
            }

            else -> {
                log.error(marker.and(Markers.append("AddPixResult", addPixResult)), "AddPix")
                StandardHttpResponses.serverError("Error to add pix")
            }
        }
    }

    fun mapFrom(
        bill: Bill,
        member: Member,
        warningCode: WarningCode? = null,
        possibleDuplicateBills: List<PossibleDuplicate> = listOf(),
    ): BillTO {
        return with(bill) {
            BillTO(
                id = billId.value,
                description = description,
                amount = amount,
                amountTotal = amountTotal,
                interest = interest,
                fine = fine,
                discount = discount,
                paymentLimitTime = paymentLimitTime.format(timeFormat),
                dueDate = dueDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                status = convertToLegacyStatusTO(bill.status),
                createdOn = Instant.ofEpochMilli(created).atZone(brazilTimeZone).format(dateTimeFormat),
                billType = billType.name,
                recipient = billTOBuilder.convertToRecipientTo(recipient),
                billRecipient = billTOBuilder.convertToRecipientTo(recipient),
                barcode = barcode?.digitable,
                paidDate = paidDate?.let {
                    Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(DateTimeFormatter.ISO_LOCAL_DATE)
                },
                assignor = assignor.orEmpty(),
                overdue = this.takeIf { it.status == BillStatus.ACTIVE }?.isOverdue(),
                source = source.toBillSourceTO(),
                payer = payer.toPayerTO(),
                warningCode = warningCode?.name ?: bill.getWarningCode().name,
                amountCalculationModel = amountCalculationModel.name,
                effectiveDueDate = effectiveDueDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                fichaCompensacaoType = mapFichaCompensacaoType(fichaCompensacaoType, recipient),
                amountPaid = amountPaid,
                possibleDuplicateBills = possibleDuplicateBills.map {
                    PossibleDuplicateBillTO(
                        billId = it.billId.value,
                        dueDate = it.dueDate.format(
                            dateFormat,
                        ),
                    )
                },
                markedAsPaidBy = bill.markedAsPaidBy?.value,
                subscriptionFee = this.subscriptionFee,
                fridaySubscription = this.subscriptionFee,
                securityValidationResult = securityValidationResult?.firstOrNull(),
                availablePaymentMethods = bill.availablePaymentMethods(member),
                transactionCorrelationId = this.transactionCorrelationId,
                batchSchedulingId = this.schedule?.batchSchedulingId?.value,
                internalSettlement = this.paymentMethodsDetail?.internalSettlement() ?: true,
                tags = this.tags, // FIXME - criar teste
                pixQrCodeData = pixQrCodeData?.let { billTOBuilder.toPixQRCodeDataTO(it) },
                categorySuggestions = listOf(),
                goalId = this.goalId?.value,
                imageUrl = billImageProvider.getBillImageUrl(this),
                modulesFields = emptyMap(),
            )
        }
    }
}