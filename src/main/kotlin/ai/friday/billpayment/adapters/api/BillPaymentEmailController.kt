// package ai.friday.billpayment.adapters.api
//
// import ai.friday.billpayment.adapters.notification.BillPaymentEmailSenderService
// import ai.friday.billpayment.adapters.notification.EmailAttachment
// import ai.friday.billpayment.adapters.notification.SendRawEmailRequest
// import ai.friday.billpayment.adapters.notification.SendTemplatedEmailRequest
// import ai.friday.billpayment.app.EmailAddress
// import ai.friday.billpayment.app.account.Role
// import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
// import io.micronaut.http.HttpResponse
// import io.micronaut.http.MediaType
// import io.micronaut.http.annotation.Body
// import io.micronaut.http.annotation.Controller
// import io.micronaut.http.annotation.Post
// import io.micronaut.security.annotation.Secured
// import java.util.Base64
// import net.logstash.logback.marker.Markers
// import org.slf4j.LoggerFactory
//
// @Secured(Role.Code.FRIDAY_CALLBACK)
// @Controller("/email")
// open class BillPaymentEmailController(
//    private val billPaymentEmailSenderService: BillPaymentEmailSenderService,
// ) {
//
//    private val logger = LoggerFactory.getLogger(this::class.java)
//
//    @Post("/templated")
//    open fun sendTemplatedEmail(@Body request: SendTemplatedEmailRequest): HttpResponse<String> {
//        return try {
//            billPaymentEmailSenderService.sendTemplatedReceiptEmail(
//                templateName = request.templateName,
//                templateParams = request.templateParams,
//                byteArrayWithNameAndTypes = request.attachments.map { it.toByteArrayWithNameAndType() },
//                subject = request.subject,
//                recipient = EmailAddress(request.recipient),
//            )
//            logger.info(Markers.append("recipient", request.recipient), "sendTemplatedEmail")
//            HttpResponse.ok("Email queued successfully")
//        } catch (e: Exception) {
//            logger.error("sendTemplatedEmail", e)
//            HttpResponse.serverError("Failed to send email: ${e.message}")
//        }
//    }
//
//    @Post("/raw")
//    open fun sendRawEmail(@Body request: SendRawEmailRequest): HttpResponse<String> {
//        return try {
//            billPaymentEmailSenderService.sendRawEmail(
//                senderAddress = EmailAddress(request.senderAddress),
//                displayName = request.displayName,
//                subject = request.subject,
//                body = request.body,
//                recipient = EmailAddress(request.recipient),
//                attachments = request.attachments.map { it.toByteArrayWithNameAndType() },
//                mediaType = MediaType.of(request.mediaType),
//            )
//            logger.info(Markers.append("recipient", request.recipient), "sendRawEmail")
//            HttpResponse.ok("Email queued successfully")
//        } catch (e: Exception) {
//            logger.error("sendRawEmail", e)
//            HttpResponse.serverError("Failed to send email: ${e.message}")
//        }
//    }
// }
//
// fun EmailAttachment.toByteArrayWithNameAndType(): ByteArrayWithNameAndType {
//    return ByteArrayWithNameAndType(
//        fileName = fileName,
//        mediaType = mediaType,
//        data = Base64.getDecoder().decode(data),
//    )
// }