package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.and
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.fingerprint.ConfirmDeviceResult
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.GenerateDeviceResult
import ai.friday.billpayment.app.fingerprint.MobileDeviceDetails
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.util.Base64
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/device")
@Version("2")
class DeviceFingerprintController(
    private val service: DeviceFingerprintService,
    private val accountRegisterRepository: AccountRegisterRepository,
) {

    private val logger = LoggerFactory.getLogger(DeviceFingerprintController::class.java)

    @Post
    fun postDevice(
        authentication: Authentication,
        @Header("x-device-fingerprint") deviceFingerprint: String,
    ): HttpResponse<*> {
        val logName = "DeviceFingerprintController#postDevice"

        val accountId = authentication.toAccountId()
        val deviceDetails = decodeDeviceFingerprint(deviceFingerprint)
        val marker = Markers.append("accountId", accountId)

        if (deviceDetails == null) {
            logger.warn(marker.and("details" to deviceFingerprint), "Invalid device fingerprint")
            return HttpResponse.badRequest("Invalid device fingerprint")
        }

        val result = service.generateDevice(
            accountId = accountId,
            details = deviceDetails,
        )

        marker.andAppend("result", result)
        return when (result) {
            is GenerateDeviceResult.LivenessFailed -> {
                logger.warn(marker, logName)
                StandardHttpResponses.conflict(responseTO = ResponseTO(code = "4091", message = "Liveness failed"))
            }

            GenerateDeviceResult.AlreadyActive,
            GenerateDeviceResult.InvalidFingerprint,
            -> {
                logger.warn(marker, logName)
                StandardHttpResponses.conflict(responseTO = ResponseTO(code = "4092", message = "Invalid fingerprint"))
            }

            GenerateDeviceResult.AccountClosed,
            GenerateDeviceResult.AccountNotFound,
            -> {
                logger.warn(marker, logName)
                StandardHttpResponses.conflict(responseTO = ResponseTO(code = "4093", message = "Invalid account"))
            }

            is GenerateDeviceResult.Success -> {
                logger.info(marker, logName)
                HttpResponse.created(
                    PostDeviceResponseTO(
                        type = result.liveness?.let { DeviceRequestTypeTO.LIVENESS } ?: DeviceRequestTypeTO.NO_CHALLENGE,
                        livenessId = result.liveness?.value,
                    ),
                )
            }
        }
    }

    @Put
    fun putDevice(
        authentication: Authentication,
        @Header("x-device-fingerprint") deviceFingerprint: String,
        @Body livenessRequestTO: PutDeviceRequestTO,
    ): HttpResponse<*> {
        val logName = "DeviceFingerprintController#putDevice"
        val markers = Markers.append("accountId", authentication.toAccountId())
        val accountId = authentication.toAccountId()
        val deviceDetails = decodeDeviceFingerprint(deviceFingerprint)
        val accountRegister = try {
            accountRegisterRepository.findByAccountId(accountId)
        } catch (e: Exception) {
            logger.warn(markers.and("details" to deviceFingerprint), "Account not found")
            return HttpResponse.badRequest("Account not found")
        }

        if (accountRegister.livenessId == null) {
            logger.warn(markers.and("details" to deviceFingerprint), "Account without liveness")
            return HttpResponse.badRequest("Account without liveness")
        }

        if (deviceDetails == null) {
            logger.warn(markers.and("details" to deviceFingerprint), "Invalid device fingerprint")
            return HttpResponse.badRequest("Invalid device fingerprint")
        }

        val result = service.confirmDevice(
            accountId = accountId,
            details = deviceDetails,
            livenessId = when (accountRegister.livenessId.provider) {
                LivenessProvider.CAF -> accountRegister.livenessId
                LivenessProvider.FACETEC -> LivenessId(value = livenessRequestTO.livenessId, provider = LivenessProvider.FACETEC)
            },
        )

        markers.andAppend("result", result)
        return when (result) {
            ConfirmDeviceResult.RegisteredDeviceNotFound -> {
                logger.warn(markers, logName)
                StandardHttpResponses.conflict(ResponseTO(code = "4091", message = "Registered device not found"))
            }

            is ConfirmDeviceResult.LivenessFailed -> {
                logger.warn(markers.andAppend("livenessError", result.e), logName)
                StandardHttpResponses.conflict(ResponseTO(code = "4092", message = "Registered device not found"))
            }

            ConfirmDeviceResult.LivenessDoNotMatch,
            ConfirmDeviceResult.DifferentLivenessId,
            ConfirmDeviceResult.RegisteredDeviceNotPending,
            ConfirmDeviceResult.DeviceDetailsDoNotMatch,
            ConfirmDeviceResult.EmptyFingerprint,
            -> {
                logger.warn(markers, logName)
                StandardHttpResponses.conflict(ResponseTO(code = "4093", message = "Unable to confirm device"))
            }

            ConfirmDeviceResult.Success ->
                HttpResponse.noContent<Unit>()
        }
    }

    private fun decodeDeviceFingerprint(deviceFingerprint: String): MobileDeviceDetails? = kotlin.runCatching {
        val deviceFingerprintPayload = String(Base64.getDecoder().decode(deviceFingerprint))
        return parseObjectFrom(deviceFingerprintPayload)
    }.getOrNull()
}

enum class DeviceRequestTypeTO {
    LIVENESS, NO_CHALLENGE
}

data class PostDeviceResponseTO(
    val type: DeviceRequestTypeTO,
    val livenessId: String?,
)

data class PutDeviceRequestTO(
    val type: DeviceRequestTypeTO,
    val livenessId: String,
)