package ai.friday.billpayment.adapters.api.builders

import ai.friday.billpayment.adapters.api.BaseKnownBillActions
import ai.friday.billpayment.adapters.api.BillCategoryTO
import ai.friday.billpayment.adapters.api.BillParticipantTO
import ai.friday.billpayment.adapters.api.BillParticipantsTO
import ai.friday.billpayment.adapters.api.BillScheduleTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.ModuleFieldsTO
import ai.friday.billpayment.adapters.api.PayerTO
import ai.friday.billpayment.adapters.api.PaymentDetailsTO
import ai.friday.billpayment.adapters.api.PixKeyResponseTO
import ai.friday.billpayment.adapters.api.PossibleDuplicateBillTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.RecurrenceResponseTO
import ai.friday.billpayment.adapters.api.ResponseRecipientTO
import ai.friday.billpayment.adapters.api.calculateOccurrence
import ai.friday.billpayment.adapters.api.convertToLegacyStatusTO
import ai.friday.billpayment.adapters.api.mapFichaCompensacaoType
import ai.friday.billpayment.adapters.api.toBillSourceTO
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.BillViewWrapper
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.availablePaymentMethods
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.calculateInstallmentValue
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixQRCodeDetailsResult
import ai.friday.billpayment.app.sanitizeDocumentNumber
import ai.friday.billpayment.app.wallet.Member
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@Singleton
open class BillTOBuilder(
    private val pixQrCodeCompoundDataBuilder: PixQrCodeCompoundDataBuilder?,
    val builderSteps: List<BillTOBuilderStep>,
) {

    private val orderedBuilderSteps = builderSteps.sortedBy { it.stepOrder }

    init {
        // verificar se 2 passos não possuem o mesmo nome de módulo
        val moduleNames = orderedBuilderSteps.map { it.billTOPropertyName }
        val distinctModuleNames = moduleNames.distinct()
        if (moduleNames.size != distinctModuleNames.size) {
            throw IllegalStateException("2 ou mais passos possuem o mesmo nome de módulo")
        }
    }

    @Suppress("DEPRECATION")
    fun toBillTO(billView: BillView, member: Member, category: BillCategoryTO? = null, categorySuggestion: List<BillCategoryTO?> = listOf(), imageUrl: String? = null): BillTO {
        val builderData = BillTOBuilderData(billView, member, category, categorySuggestion, imageUrl)
        val billTO = with(builderData.billView) {
            val verifiedAssignor = if (billType.isBoleto()) {
                when {
                    assignor != null -> assignor
                    else -> recipient?.name
                }
            } else {
                assignor
            }

            val baseAllowedActions = if (billType == BillType.AUTOMATIC_PIX) {
                if (dueDate > getLocalDate()) {
                    setOf(BaseKnownBillActions.IGNORE_BILL)
                } else {
                    emptySet()
                }
            } else {
                emptySet()
            }

            val moduleAllowedActions = orderedBuilderSteps.map {
                it.allowedActions(builderData)
            }.flatten()

            val allowedActions = (baseAllowedActions + moduleAllowedActions).toSet()

            val billTO = BillTO(
                id = billId.value,
                description = billDescription,
                amount = amount,
                amountTotal = amountTotal,
                amountPaid = amountPaid,
                interest = interest,
                fine = fine,
                discount = discount,
                paymentLimitTime = paymentLimitTime.format(timeFormat),
                dueDate = dueDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                status = convertToLegacyStatusTO(status),
                createdOn = createdOn.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                billType = billType.name,
                recipient = convertToRecipientTo(recipient),
                billRecipient = convertToRecipientTo(recipient),
                barcode = barCode?.digitable,
                paidDate = paidDate?.format(DateTimeFormatter.ISO_LOCAL_DATE),
                assignor = verifiedAssignor,
                overdue = null,
                source = source.toBillSourceTO(),
                payer = payerDocument?.let { PayerTO(name = payerName, document = it, alias = payerAlias) },
                warningCode = warningCode.name,
                amountCalculationModel = amountCalculationModel?.name,
                externalId = externalId,
                recurrence = recurrenceRule?.let {
                    val recurrenceId = when (source) {
                        is ActionSource.Recurrence -> source.recurrenceId.value
                        is ActionSource.WalletRecurrence -> source.recurrenceId.value
                        is ActionSource.SubscriptionRecurrence -> source.recurrenceId.value
                        else -> throw IllegalStateException("action source should not be $source")
                    }
                    RecurrenceResponseTO(
                        id = recurrenceId,
                        frequency = it.frequency.name,
                        startDate = it.startDate.format(dateFormat),
                        pattern = it.pattern,
                        endDate = it.endDate?.format(dateFormat),
                        occurrence = it.calculateOccurrence(dueDate),
                    )
                },
                schedule = schedule?.let { schedule ->
                    BillScheduleTO(
                        date = schedule.date.format(dateFormat),
                        waitingFunds = schedule.waitingFunds,
                        waitingRetry = schedule.waitingRetry,
                    )
                },
                effectiveDueDate = effectiveDueDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                fichaCompensacaoType = mapFichaCompensacaoType(fichaCompensacaoType, recipient),
                possibleDuplicateBills = emptyList(),
                markedAsPaidBy = this.markedAsPaidBy?.value,
                subscriptionFee = this.subscriptionFee,
                fridaySubscription = this.subscriptionFee,
                trackingOutdated = false,
                securityValidationResult = this.securityValidationResult?.firstOrNull(),
                availablePaymentMethods = availablePaymentMethods(builderData.member),
                transactionCorrelationId = this.transactionCorrelationId,
                batchSchedulingId = this.schedule?.batchSchedulingId?.value,
                internalSettlement = this.paymentDetails?.internalSettlement() ?: true,
                paymentDetails = when (paymentDetails) {
                    is PaymentMethodsDetailWithCreditCard -> PaymentDetailsTO(
                        installments = paymentDetails.installments,
                        installmentAmount = paymentDetails.calculateInstallmentValue(),
                        fee = (paymentDetails.fee * 100).toLong(),
                        feeAmount = paymentDetails.feeAmount,
                    )

                    is PaymentMethodsDetailWithExternalPayment,
                    is PaymentMethodsDetailWithBalance,
                    is MultiplePaymentMethodsDetail,
                    -> null

                    null -> null
                },
                tags = tags,
                category = builderData.category,
                categorySuggestions = builderData.categorySuggestion,
                pixQrCodeData = pixQrCodeData?.let { toPixQRCodeDataTO(it) },
                brand = brand,
                participants = participants?.let {
                    BillParticipantsTO(
                        owner = BillParticipantTO(walletId = it.owner.walletId.value, name = it.owner.name, imageUrlSmall = it.owner.imageUrlSmall),
                        scheduledBy = it.scheduledBy?.let { s -> BillParticipantTO(walletId = s.walletId.value, name = s.name, imageUrlSmall = s.imageUrlSmall) },
                        paidBy = it.paidBy?.let { p -> BillParticipantTO(walletId = p.walletId.value, name = p.name, imageUrlSmall = p.imageUrlSmall) },
                    )
                },
                goalId = goalId?.value,
                imageUrl = builderData.imageUrl,
                allowedActions = allowedActions,
                modulesFields = orderedBuilderSteps.mapNotNull {
                    it.buildExtraFields(builderData)?.let { moduleData ->
                        it.billTOPropertyName to moduleData
                    }
                }.associate { it },
            )

            if (ai.friday.billpayment.app.bill.BillStatus.ACTIVE == status) {
                billTO.overdue = isOverdue
            }
            billTO
        }
        return billTO
    }

    fun mapFrom(wrapper: BillViewWrapper, member: Member, categories: Map<PFMCategoryId, BillCategoryTO>, imageUrl: String?): BillTO {
        val category = wrapper.bill.categoryId?.let { categories.getOrDefault(it, null) }
        val categorySuggestions = wrapper.bill.categorySuggestions.map { categories.getOrDefault(it.categoryId, null) }

        return toBillTO(wrapper.bill, member, category, categorySuggestions, imageUrl).copy(
            possibleDuplicateBills = wrapper.possibleDuplicateBills.map {
                PossibleDuplicateBillTO(
                    billId = it.billId.value,
                    dueDate = it.dueDate.format(
                        dateFormat,
                    ),
                )
            },
            trackingOutdated = wrapper.trackingOutdated,
            participants = wrapper.participants?.let {
                BillParticipantsTO(
                    owner = BillParticipantTO(walletId = it.owner.walletId.value, name = it.owner.name, imageUrlSmall = it.owner.imageUrlSmall),
                    scheduledBy = it.scheduledBy?.let { s -> BillParticipantTO(walletId = s.walletId.value, name = s.name, imageUrlSmall = s.imageUrlSmall) },
                    paidBy = it.paidBy?.let { p -> BillParticipantTO(walletId = p.walletId.value, name = p.name, imageUrlSmall = p.imageUrlSmall) },
                )
            },
        )
    }

    fun toPixQrCodeResponseTO(pixQRCodeDetailsResult: PixQRCodeDetailsResult): PixQRCodeResponseTO {
        return PixQRCodeResponseTO(
            pixQrCodeData = toPixQRCodeDataTO(pixQRCodeDetailsResult.qrCodeInfo!!),
            ispb = pixQRCodeDetailsResult.pixKeyDetails?.holder?.ispb,
            institutionName = pixQRCodeDetailsResult.pixKeyDetails?.holder?.institutionName,
            document = pixQRCodeDetailsResult.pixKeyDetails?.owner?.document,
            name = pixQRCodeDetailsResult.pixKeyDetails?.owner?.name,
        )
    }

    /*
    Conta Pagamento deve retornar como Conta Corrente até o frontend estar preparado
 */
    fun toPixKeyResponseTO(pixKeyDetails: PixKeyDetails) = PixKeyResponseTO(
        type = pixKeyDetails.key.type,
        value = pixKeyDetails.key.value,
        routingNo = pixKeyDetails.holder.routingNo,
        accountNo = pixKeyDetails.holder.accountNo,
        accountDv = pixKeyDetails.holder.accountDv,
        ispb = pixKeyDetails.holder.ispb,
        institutionName = pixKeyDetails.holder.institutionName,
        document = pixKeyDetails.owner.document,
        accountType = if (pixKeyDetails.holder.accountType == AccountType.PAYMENT) AccountType.CHECKING else pixKeyDetails.holder.accountType,
        name = pixKeyDetails.owner.name,
    )

    fun convertToRecipientTo(recipient: Recipient?): ResponseRecipientTO? {
        val showReceiptAccountDetails = recipient?.pixQrCodeData == null
        return recipient?.let {
            ResponseRecipientTO(
                document = recipient.document?.sanitizeDocumentNumber().orEmpty(),
                alias = recipient.alias,
                name = recipient.name,
                bankDetails = if (showReceiptAccountDetails) {
                    convertToBankDetailsTO(recipient.bankAccount)
                } else {
                    null
                },
                pixKey = if (showReceiptAccountDetails) {
                    recipient.pixKeyDetails?.let { toPixKeyResponseTO(it) }
                } else {
                    null
                },
                pixQrCodeData = recipient.pixQrCodeData?.let { toPixQRCodeDataTO(it) },
            )
        }
    }

    fun convertToBankDetailsTO(bankAccount: BankAccount?): RecipientBankDetailsTO? {
        return bankAccount?.let {
            RecipientBankDetailsTO(
                accountType = bankAccount.accountType,
                bankNo = bankAccount.bankNo,
                routingNo = bankAccount.routingNo,
                accountNo = bankAccount.accountNo,
                accountDv = bankAccount.accountDv,
                ispb = bankAccount.ispb,
            )
        }
    }

    fun toPixQRCodeDataTO(pixQrCodeData: PixQrCodeData): PixQrCodeDataTO {
        return PixQrCodeDataTO(
            pixId = pixQrCodeData.pixId,
            fixedAmount = pixQrCodeData.fixedAmount,
            type = pixQrCodeData.type,
            info = pixQrCodeData.info,
            additionalInfo = pixQrCodeData.additionalInfo,
            expiration = pixQrCodeData.expiration,
            originalAmount = pixQrCodeData.originalAmount,
            recurringPixData = pixQrCodeData.automaticPixRecurringDataJson?.let {
                getPixQrCodeCompoundDataBuilder().toResponseTO(it).getOrThrow()
            },
        )
    }

    private fun getPixQrCodeCompoundDataBuilder(): PixQrCodeCompoundDataBuilder {
        return pixQrCodeCompoundDataBuilder ?: throw IllegalStateException("pixQrCodeCompoundDataBuilder is not initialized")
    }
}

data class PixQRCodeResponseTO(
    val pixQrCodeData: PixQrCodeDataTO,
    val ispb: String? = null,
    val institutionName: String? = null,
    val document: String? = null,
    val name: String? = null,
)

data class PixQrCodeDataTO(
    val type: PixQrCodeType,
    val info: String,
    val additionalInfo: Map<String, String>?,
    val pixId: String?,
    val fixedAmount: Long?,
    val expiration: ZonedDateTime?,
    val originalAmount: Long?,
    val recurringPixData: PixQrCompoundDataTOData?,
)

interface PixQrCompoundDataTOData

interface PixQrCodeCompoundDataBuilder {
    fun toResponseTO(recurringPixDataJson: String): Result<PixQrCompoundDataTOData>
}

interface BillTOBuilderStep {
    val stepOrder: Int
    val billTOPropertyName: String
    fun buildExtraFields(builderData: BillTOBuilderData): ModuleFieldsTO?
    fun allowedActions(builderData: BillTOBuilderData): Set<String>
}

data class BillTOBuilderData(
    val billView: BillView,
    val member: Member,
    val category: BillCategoryTO? = null,
    val categorySuggestion: List<BillCategoryTO?> = listOf(),
    val imageUrl: String? = null,
)