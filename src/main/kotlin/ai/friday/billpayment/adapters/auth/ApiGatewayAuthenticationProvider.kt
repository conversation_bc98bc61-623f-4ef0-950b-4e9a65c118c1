package ai.friday.billpayment.adapters.auth

import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.authentication.AuthenticationRequest
import io.micronaut.security.authentication.AuthenticationResponse
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val X_AUTH_VALIDATED_HEADER = "X-Auth-Validated"
private const val X_USER_SUB_HEADER = "X-User-Sub"
private const val X_USER_EMAIL_HEADER = "X-User-Email"
private const val X_USER_GROUPS_HEADER = "X-User-Groups"
private const val X_COGNITO_USERNAME_HEADER = "X-Cognito-Username"
private const val X_TENANT_ID_HEADER = "X-Tenant-Id"

private const val API_GATEWAY_SOURCE = "api-gateway"

@Singleton
class ApiGatewayAuthenticationProvider(
    @Property(name = "backoffice.apigateway.username") private val apiGatewayUsername: String,
    @Property(name = "backoffice.apigateway.password") private val apiGatewayPassword: String,
) : BillPaymentAuthenticationProvider() {
    private val logger = LoggerFactory.getLogger(ApiGatewayAuthenticationProvider::class.java)

    override fun billPaymentAuthenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Pair<String, AuthenticationResponse> {
        val logName = "ApiGatewayAuthenticationProvider"

        val authenticatorName = this::class.java.simpleName
        val markers = append("authenticator", authenticatorName)

        // Verificar se a requisição tem o header de validação do API Gateway
        val fromApiGateway = httpRequest?.headers?.get(X_AUTH_VALIDATED_HEADER)

        if (fromApiGateway != "true") {
            logger.warn(markers.andAppend("context", "Request without API Gateway validation header"), logName)
            return Pair(authenticatorName, AuthenticationResponse.failure("Not from API Gateway"))
        }

        // Extrair dados do usuário dos headers
        val userSub = httpRequest.headers.get(X_USER_SUB_HEADER)
        val userEmail = httpRequest.headers.get(X_USER_EMAIL_HEADER)
        val userGroups = httpRequest.headers.get(X_USER_GROUPS_HEADER)
        val cognitoUsername = httpRequest.headers.get(X_COGNITO_USERNAME_HEADER)
        val tenantHeader = httpRequest.headers.get(X_TENANT_ID_HEADER)

        markers.andAppend("tenantId", tenantHeader ?: "N/A")

        if (userSub.isNullOrBlank() || userEmail.isNullOrBlank() || cognitoUsername.isNullOrBlank()) {
            logger.warn(markers.andAppend("context", "Missing required user sub from API Gateway headers"), logName)
            return Pair(authenticatorName, AuthenticationResponse.failure("Missing user information"))
        }

        // Processar grupos/roles do usuário
        val roles = parseUserGroups(userGroups)

        // Criar attributes map com informações do usuário
        val attributes = mapOf(
            "sub" to userSub,
            "email" to (userEmail ?: ""),
            "cognitoUsername" to cognitoUsername,
            "source" to API_GATEWAY_SOURCE,
        )

        if (roles.isEmpty()) {
            logger.warn(markers.andAppend("context", "Missing required roles"), logName)
            return Pair(authenticatorName, AuthenticationResponse.failure("Missing user information"))
        }

        if (authenticationRequest.identity != apiGatewayUsername || authenticationRequest.secret != apiGatewayPassword) {
            logger.warn(
                markers
                    .andAppend("context", "Invalid API Gateway credentials")
                    .andAppend("identity", authenticationRequest.identity),
                logName,
            )
            return Pair(authenticatorName, AuthenticationResponse.failure("Invalid API Gateway credentials"))
        }

        logger.info(
            markers
                .andAppend("context", "Successful authentication from API Gateway")
                .andAppend("userSub", userSub)
                .andAppend("email", userEmail)
                .andAppend("roles", roles)
                .andAppend("cognitoUsername", cognitoUsername),
            logName,
        )

        return Pair(
            authenticatorName,
            AuthenticationResponse.success(userSub, roles, attributes),
        )
    }

    private fun parseUserGroups(userGroups: String?): List<String> {
        if (userGroups.isNullOrBlank()) {
            return emptyList()
        }

        // Cognito groups podem vir como string separada por vírgulas ou como array JSON
        val cognitoGroups = when {
            userGroups.startsWith("[") && userGroups.endsWith("]") -> {
                // Formato JSON array: ["admin", "user"]
                userGroups.removeSurrounding("[", "]")
                    .split(",")
                    .map { it.trim().removeSurrounding("\"") }
                    .filter { it.isNotBlank() }
            }

            userGroups.contains(",") -> {
                // Formato CSV: admin,user
                userGroups.split(",").map { it.trim() }.filter { it.isNotBlank() }
            }

            else -> {
                // Single group
                listOf(userGroups.trim())
            }
        }.ifEmpty { emptyList() }

        // Mapear grupos do Cognito para roles da aplicação
        return cognitoGroups.map { it.uppercase() }.distinct()
    }
}

/**
 * Extension functions para acessar dados do usuário autenticado via API Gateway
 */
fun Authentication.getUserSub(): String {
    return this.name
}

fun Authentication.getUserEmail(): String? {
    return this.attributes["email"] as? String
}

fun Authentication.getCognitoUsername(): String? {
    return this.attributes["cognitoUsername"] as? String
}

fun Authentication.isFromApiGateway(): Boolean {
    return this.attributes["source"] == API_GATEWAY_SOURCE
}