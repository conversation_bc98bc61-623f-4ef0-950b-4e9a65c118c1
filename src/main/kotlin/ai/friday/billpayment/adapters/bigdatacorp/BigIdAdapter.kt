package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.DocumentQuality
import ai.friday.billpayment.app.integrations.DocumentMustBeOpenedException
import ai.friday.billpayment.app.integrations.DocumentOCRParser
import ai.friday.billpayment.app.integrations.DocumentOCRParserException
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.FaceMatcher
import ai.friday.billpayment.app.integrations.InvalidDocumentImageException
import ai.friday.billpayment.app.register.kyc.FaceMatchResult
import ai.friday.billpayment.app.register.kyc.FaceMatcherException
import ai.friday.billpayment.app.stripAccents
import ai.friday.billpayment.app.stripSpacesAndPunctuation
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.flatMap
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.reactivex.Flowable
import java.io.InputStream
import java.time.Duration
import java.time.Instant.EPOCH
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Base64
import java.util.Locale
import kotlin.math.roundToInt
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private val bigIdDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

@FridayMePoupe
class BigIdAdapter(
    @param:Client(value = "\${integrations.bigdatacorp.bigIdHost}") private val httpClient: RxHttpClient,
    bigDataAuth: BigDataAuth,
) : DocumentOCRParser, FaceMatcher {

    @field:Property(name = "integrations.bigdatacorp.ocrPath")
    lateinit var ocrPath: String

    @field:Property(name = "integrations.bigdatacorp.imageQualityPath")
    lateinit var imageQualityPath: String

    @field:Property(name = "integrations.bigdatacorp.faceMatchPath")
    lateinit var faceMatchPath: String

    private val bigDataTokenWrapper = BigDataAccessTokenWrapper(bigDataAuth = bigDataAuth)

    override fun parseDocument(imageBase64: String): Either<Exception, DocumentInfo> {
        val httpRequest = buildGetOCRRequest(imageBase64)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(BigIdResultTO::class.java),
            Argument.of(BigIdResultTO::class.java),
        )
        val response = fetch(call)
        return extractDocumentInfo(response)
    }

    override fun parseDocument(
        frontImageBase64: String,
        backImageBase64: String,
        documentType: DocumentType,
    ): Either<Exception, DocumentInfo> {
        val httpRequest = buildGetOCRRequest(frontImageBase64, backImageBase64, documentType)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(BigIdResultTO::class.java),
            Argument.of(BigIdResultTO::class.java),
        )
        val response = fetch(call)
        return extractDocumentInfo(response)
    }

    override fun match(documentImage: InputStream, selfieImage: InputStream): Either<Exception, FaceMatchResult> {
        val request = buildFaceMatchRequest(documentImage, selfieImage)
        val call =
            httpClient.retrieve(request, Argument.of(BigIdResultTO::class.java), Argument.of(BigIdResultTO::class.java))
        val response = fetch(call)

        return extractFaceMatchInfo(response)
    }

    private inline fun <reified T : Any> Either<Exception, T>.log(): Either<Exception, T> {
        val logName = "BigDataCorpGet${T::class.simpleName}"
        this.fold(ifLeft = { logger.error(logName, it) }, ifRight = { logger.info(append("response", it), logName) })
        return this
    }

    private inline fun <reified T : Any> fetch(call: Flowable<T>): Either<Exception, T> {
        return try {
            Either.Right(call.firstOrError().blockingGet())
        } catch (e: HttpClientResponseException) {
            when (e.status.code) {
                401 -> {
                    bigDataTokenWrapper.invalidate()
                    Either.Left<Exception>(DocumentOCRParserException(e))
                }

                512 -> Either.Left<Exception>(DocumentOCRParserException(e))
                else ->
                    Either.Left(
                        InvalidDocumentImageException(
                            "Invalid document image: ${e.response.getBody(String::class.java)}",
                            e,
                        ),
                    )
            }
        } catch (e: Exception) {
            Either.Left(DocumentOCRParserException(e))
        }.log<T>()
    }

    private fun buildFaceMatchRequest(
        baseFaceImage: InputStream,
        targetFaceImage: InputStream,
    ): MutableHttpRequest<Map<String, List<String>>> {
        val base64Encoder = Base64.getEncoder()
        val requestBody = mapOf(
            "Parameters" to listOf(
                "BASE_FACE_IMG=${base64Encoder.encodeToString(baseFaceImage.readAllBytes())}",
                "MATCH_IMG=${base64Encoder.encodeToString(targetFaceImage.readAllBytes())}",
            ),
        )
        return HttpRequest.POST(faceMatchPath, requestBody).contentType(MediaType.APPLICATION_JSON)
            .bearerAuth(bigDataTokenWrapper.fetch().value).accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private fun buildGetOCRRequest(imageBase64: String): MutableHttpRequest<Map<String, List<String>>> {
        val requestBody = mapOf("Parameters" to listOf("DOC_IMG=$imageBase64"))
        return HttpRequest.POST(ocrPath, requestBody).contentType(MediaType.APPLICATION_JSON)
            .bearerAuth(bigDataTokenWrapper.fetch().value).accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private fun buildGetOCRRequest(
        frontImageBase64: String,
        backImageBase64: String,
        documentType: DocumentType,
    ): MutableHttpRequest<Map<String, List<String>>> {
        val requestBody =
            mapOf(
                "Parameters" to listOf(
                    "DOC_TYPE=${documentType.value}",
                    "DOC_IMG_A=$frontImageBase64",
                    "DOC_IMG_B=$backImageBase64",
                ),
            )
        return HttpRequest.POST(ocrPath, requestBody).contentType(MediaType.APPLICATION_JSON)
            .bearerAuth(bigDataTokenWrapper.fetch().value).accept(MediaType.APPLICATION_JSON_TYPE)
    }

    override fun fetchDocumentQuality(imageBase64: String): Either<Exception, DocumentQuality> {
        val httpRequest = buildDocumentQualityRequest(imageBase64)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(ImageQualityResultTO::class.java),
            Argument.of(ImageQualityResultTO::class.java),
        )
        val response = fetch(call)
        return response.map { it.toDocumentQuality() }
    }

    private fun buildDocumentQualityRequest(imageBase64: String): MutableHttpRequest<*> {
        val requestBody = mapOf("image" to imageBase64, "classify" to true)
        return HttpRequest.POST(imageQualityPath, requestBody).contentType(MediaType.APPLICATION_JSON)
            .bearerAuth(bigDataTokenWrapper.fetch().value).accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private fun ImageQualityResultTO.toDocumentQuality() = DocumentQuality(
        typeOfDocument = DocumentType.of(this.typeOfDocument),
        containsFace = this.containsFace,
    )

    private data class ImageQualityResultTO(
        val typeOfDocument: String,
        val containsFace: Boolean,
    )

    private fun extractFaceMatchInfo(response: Either<Exception, BigIdResultTO>): Either<Exception, FaceMatchResult> {
        val documentMatchResult = 80
        val documentDoesNotMatchResult = -800

        val documentResult = response.getOrElse {
            logger.error("extractFaceMatchInfo", it)
            return FaceMatcherException("Could not find BigIdResponse", it).left()
        }

        if (documentResult.resultCode !in listOf(documentMatchResult, documentDoesNotMatchResult)) {
            logger.error(append("result", documentResult), "extractFaceMatchInfo")
            return FaceMatcherException("BigId result code ${documentResult.resultCode} is not expected").left()
        }

        if (documentResult.estimatedInfo == null) {
            logger.warn(append("result", documentResult).andAppend("estimatedInfo", "null"), "extractFaceMatchInfo")
        }

        val similarity = documentResult.estimatedInfo?.get("Similarity")?.toDouble()?.roundToInt() ?: 0
        return FaceMatchResult(
            match = documentResult.resultCode == documentMatchResult,
            similarity = similarity,
        ).right()
    }

    private fun extractDocumentInfo(response: Either<Exception, BigIdResultTO>): Either<Exception, DocumentInfo> {
        val foundInfo = 70

        return response.flatMap {
            logger.info(append("bigDataResponse", it), "buildDocumentInfo")
            if (it.resultCode != foundInfo) {
                return Either.Left(InvalidDocumentImageException("Invalid document image"))
            }
            if (it.documentInfo == null) {
                return Either.Left(InvalidDocumentImageException("Missing documentInfo"))
            }
            buildDocumentInfo(it.documentInfo)
        }
    }

    internal fun buildDocumentInfo(map: Map<String, String>): Either<Exception, DocumentInfo> {
        return try {
            when (val documentType = DocumentType.of(map["DOCTYPE"].orEmpty())) {
                DocumentType.CNH, DocumentType.CNHV2 -> buildCNHDocumentInfo(map).right()
                else -> buildRgDocumentInfo(map).right()
            }
        } catch (e: NoSuchElementException) {
            Either.Left(e)
        }
    }

    private fun buildRgDocumentInfo(map: Map<String, String>): DocumentInfo {
        val (city, state) = extractCityAndStateFromRG(map)

        val (cpf, name, motherName, fatherName) = extractCPFAndParentNames(map)

        return DocumentInfo(
            name = name,
            cpf = cpf,
            rg = map["IDENTIFICATIONNUMBER"].orEmpty(),
            birthDate = map["BIRTHDATE"]?.let { date -> parseBrazilDate(date) },
            fatherName = fatherName,
            motherName = motherName,
            cnhNumber = "",
            orgEmission = processOrgEmission(map["DOCEMISSIONPLACE"].orEmpty()),
            expeditionDate = map["EXPEDITIONDATE"]?.let { date -> parseBrazilDate(date) },
            docType = DocumentType.RG,
            birthCity = city,
            birthState = state,
        )
    }

    internal fun buildCNHDocumentInfo(map: Map<String, String>): DocumentInfo {
        if (map["SIDE"] == null || map.getValue("SIDE") != FULL_DOCUMENT) {
            throw DocumentMustBeOpenedException()
        }

        val (city, state) = extractCityAndState(map)

        val (cpf, name, motherName, fatherName) = extractCPFAndParentNames(map)

        return DocumentInfo(
            name = name,
            cpf = cpf,
            rg = map["IDENTIFICATIONNUMBER"].orEmpty(),
            birthDate = map["BIRTHDATE"]?.let { date -> parseBrazilDate(date) },
            fatherName = fatherName,
            motherName = motherName,
            cnhNumber = map["CNHNUMBER"],
            orgEmission = "DETRAN",
            expeditionDate = map["EXPEDITIONDATE"]?.let { date -> parseBrazilDate(date) },
            docType = DocumentType.CNH,
            birthCity = city,
            birthState = state,
        )
    }

    private fun extractCityAndState(map: Map<String, String>): Pair<String, String> {
        val placeOfbirth = map["PLACEOFBIRTH"] ?: map["PLACEOFEMISSION"]
        return placeOfbirth?.let { place ->
            splitCityAndState(place)
        } ?: Pair("", locateUFMonogram(map["IDENTIFICATIONUF"].orEmpty()))
    }

    private fun extractCityAndStateFromRG(map: Map<String, String>): Pair<String, String> {
        return map["PLACEOFBIRTH"]?.let { placeOfBirth ->
            splitCityAndStateFromRG(placeOfBirth)
        } ?: Pair("", locateUFMonogram(map["IDENTIFICATIONUF"].orEmpty()))
    }

    private fun extractCPFAndParentNames(map: Map<String, String>): DocumentAndNames {
        val cpf = map["CPF"].orEmpty()
        val name = map["NAME"].orEmpty()
        val originalMotherName = map["MOTHERNAME"].orEmpty().trim()
        val originalFatherName = map["FATHERNAME"].orEmpty().trim()
        val motherName = originalMotherName.replace(originalFatherName, "").trim()
        val fatherName = originalFatherName.replace(originalMotherName, "").trim()

        logger.info(
            append("cpf", cpf).andAppend("originalMotherName", originalMotherName)
                .andAppend("originalFatherName", originalFatherName).andAppend("motherName", motherName)
                .andAppend("fatherName", fatherName),
            "buildDocumentInfo",
        )
        return DocumentAndNames(
            cpf,
            name,
            motherName,
            fatherName,
        )
    }

    data class DocumentAndNames(
        val cpf: String,
        val name: String,
        val motherName: String,
        val fatherName: String,
    )

    class BigIdResultTO(
        @JsonProperty("DocInfo") val documentInfo: Map<String, String>?,
        @JsonProperty("EstimatedInfo") val estimatedInfo: Map<String, String>?,
        @JsonProperty("TicketId") val ticketId: String,
        @JsonProperty("ResultCode") val resultCode: Int,
        @JsonProperty("ResultMessage") val resultMessage: String,
        @JsonProperty("Questions") val questions: List<String>?,
    )

    companion object {
        private val logger = LoggerFactory.getLogger(BigIdAdapter::class.java)
    }
}

private fun parseBrazilDate(date: String): LocalDate {
    return LocalDate.parse(date, bigIdDateFormatter)
}

private fun locateUFMonogram(rawUF: String): String {
    return states.firstOrNull {
        it.monogram == rawUF.uppercase() || it.fullName == rawUF.uppercase()
    }?.let { it.monogram } ?: ""
}

@ConfigurationProperties("integrations.bigdatacorp")
class BigDataAuthConfiguration {
    lateinit var authPath: String
    lateinit var login: String
    lateinit var password: String
    var accesTokenExpiration: Int = 1
}

class BigDataAccessTokenWrapper(
    private val bigDataAuth: BigDataAuth,
    var bigDataAccessToken: BigDataAccessToken = expiredToken,
) {

    private val offset = Duration.ofMinutes(1)

    fun fetch(): BigDataAccessToken {
        if (bigDataAccessToken.expiration.minus(offset).isBefore(getZonedDateTime())) {
            bigDataAccessToken = bigDataAuth.fetchAccessToken()
        }

        return bigDataAccessToken
    }

    fun invalidate() {
        bigDataAccessToken = expiredToken
    }

    companion object {
        private val expiredToken = BigDataAccessToken("EXPIRED", expiration = EPOCH.atZone(brazilTimeZone))
    }
}

data class BigDataAccessToken(
    val value: String,
    val expiration: ZonedDateTime,
)

@FridayMePoupe
open class BigDataAuth(
    @param:Client(value = "\${integrations.bigdatacorp.authHost}") private val httpClient: RxHttpClient,
    private val configuration: BigDataAuthConfiguration,
) {

    private val bigDataExpirationDateTimeFormatter =
        DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss z", Locale.US)

    open fun fetchAccessToken(): BigDataAccessToken {
        val requestBody = mapOf(
            "login" to configuration.login,
            "password" to configuration.password,
            "expires" to configuration.accesTokenExpiration,
        )
        val request = HttpRequest.POST(configuration.authPath, requestBody).contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            request,
            Argument.of(AuthResponseTO::class.java),
            Argument.STRING,
        )
        logger.info(append("expiration", configuration.accesTokenExpiration), "BigDataFetchAccessToken")
        try {
            val response = call.firstOrError().blockingGet()
            val expiration = runCatching {
                ZonedDateTime.parse(response.expiration, bigDataExpirationDateTimeFormatter)
            }.recoverCatching {
                logger.warn("BigDataFetchAccessToken", it)
                ZonedDateTime.parse(response.expiration, DateTimeFormatter.ISO_DATE_TIME)
            }

            return BigDataAccessToken(
                value = response.token,
                expiration = expiration.getOrThrow(),
            )
        } catch (e: HttpClientResponseException) {
            logger.error(append("response", e.response.getBody(String::class.java)).andAppend("status", e.status).andAppend("responseLength", e.response.body.toString().length), "BigDataFetchAccessToken")
            throw e
        } catch (e: Exception) {
            logger.error("BigDataFetchAccessToken", e)
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BigDataAuth::class.java)
    }
}

fun processOrgEmission(orgEmission: String): String {
    val normalizedOrgEmission = orgEmission.stripAccents().stripSpacesAndPunctuation().lowercase()
    val acronym = NORMALIZED_ORG_EMISSION_MAP[normalizedOrgEmission]
    if (acronym != null) {
        return acronym
    }

    if (normalizedOrgEmission.length > 7) {
        return ""
    }

    if (normalizedOrgEmission.take(3) in ORG_EMISSION_PREFIX_LIST) {
        return orgEmission
    }

    return ""
}

class AuthResponseTO(
    val expiration: String,
    val message: String,
    val success: Boolean,
    val token: String,
    @JsonProperty("tokenID") val tokenId: String,
)

internal const val ONLY_FRONT = "A"
internal const val ONLY_BACK = "B"
internal const val FULL_DOCUMENT = "C"

data class StatesNamesAndMonogram(
    val fullName: String,
    val monogram: String,
)

val states = listOf(
    StatesNamesAndMonogram("ACRE", "AC"),
    StatesNamesAndMonogram("ALAGOAS", "AL"),
    StatesNamesAndMonogram("AMAPÁ", "AP"),
    StatesNamesAndMonogram("AMAZONAS", "AM"),
    StatesNamesAndMonogram("BAHIA", "BA"),
    StatesNamesAndMonogram("CEARÁ", "CE"),
    StatesNamesAndMonogram("DISTRITO FEDERAL", "DF"),
    StatesNamesAndMonogram("ESPÍRITO SANTO", "ES"),
    StatesNamesAndMonogram("GOIÁS", "GO"),
    StatesNamesAndMonogram("MARANHÃO", "MA"),
    StatesNamesAndMonogram("MATO GROSSO", "MT"),
    StatesNamesAndMonogram("MATO GROSSO DO SUL", "MS"),
    StatesNamesAndMonogram("MINAS GERAIS", "MG"),
    StatesNamesAndMonogram("PARÁ", "PA"),
    StatesNamesAndMonogram("PARAÍBA", "PB"),
    StatesNamesAndMonogram("PARANÁ", "PR"),
    StatesNamesAndMonogram("PERNAMBUCO", "PE"),
    StatesNamesAndMonogram("PIAUÍ", "PI"),
    StatesNamesAndMonogram("RIO DE JANEIRO", "RJ"),
    StatesNamesAndMonogram("RIO GRANDE DO NORTE", "RN"),
    StatesNamesAndMonogram("RIO GRANDE DO SUL", "RS"),
    StatesNamesAndMonogram("RONDÔNIA", "RO"),
    StatesNamesAndMonogram("RORAIMA", "RR"),
    StatesNamesAndMonogram("SANTA CATARINA", "SC"),
    StatesNamesAndMonogram("SÃO PAULO", "SP"),
    StatesNamesAndMonogram("SERGIPE", "SE"),
    StatesNamesAndMonogram("TOCANTINS", "TO"),
)

fun splitCityAndState(cityAndState: String): Pair<String, String>? {
    val parts = cityAndState.uppercase().split("[,/]".toRegex()).map { it.trim() }

    if (parts.size != 2 || !states.map { it.monogram }.contains(parts[1])) {
        return null
    }

    return Pair(parts[0], parts[1])
}

fun splitCityAndStateFromRG(cityAndState: String): Pair<String, String>? {
    val cityAndStateTrimmed = cityAndState.uppercase().trim()
    val state = cityAndStateTrimmed.takeLast(2)
    val city = cityAndStateTrimmed.dropLast(2).trim()

    if (!states.map { it.monogram }.contains(state)) {
        return null
    }

    return Pair(city, state)
}