package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.feature.RequiresCelcoin
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.transaction.UndoTransaction
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@RequiresCelcoin
@Controller("/celcoin")
@FridayMePoupe
class CelcoinCallbackController(
    private val undoTransaction: UndoTransaction,
    private val transactionRepository: TransactionRepository,
) {

    // 5 GRANDE VOLUME DE TRANSAÇÕES.
    private fun isRetryable(errorCode: Int) = listOf(5).contains(errorCode)

    @Secured(Role.Code.CELCOIN_CALLBACK)
    @Get("/callback")
    @Put("/callback")
    @Delete("/callback")
    @Post("/callback")
    fun callback(@Body callback: CallbackTO): HttpResponse<Unit> {
        LOG.warn(append("callback", callback).andAppend("context", "não faz mais TED pela celcoin"), "CelcoinBankTransferCallback")

        return HttpResponse.ok()
    }

    @Secured(Role.Code.CELCOIN_CALLBACK)
    @Post("/eda/callback")
    fun recieveEDACallback(callbackData: EDACallbackTO): HttpResponse<*> {
        LOG.info(append("EDACallbackData", callbackData), "CelcoinEDACallback")

        return when (callbackData.event) {
            "EDA_AUTOMATIC_PAYMENT" -> HttpResponse.status<Unit>(HttpStatus.MOVED_PERMANENTLY)

            "EDA_INVOICE_WARNING",
            "EDA_AUTOMATIC_PAYMENT_SUCCESS",
            "EDA_AUTOMATIC_PAYMENT_ERROR",
            "EDA_SUBSCRIPTION_STATUS",
            -> HttpResponse.ok<Unit>()

            else -> HttpResponse.badRequest<Unit>()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CelcoinCallbackController::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class EDACallbackTO(
    val event: String,
    val invoiceId: Int,
    val barCode: String,
    val dueDate: String,
    val amount: Double,
    val subscription: EDACallbackSubscriptionTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EDACallbackSubscriptionTO(
    val subscriptionId: Int,
    val externalTerminal: String,
    val utility: EDACallbackSubscriptionUtilityTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EDACallbackSubscriptionUtilityTO(
    val utilityId: Int,
    val description: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CallbackTO(
    @field:JsonProperty("NsuExterno") val nsuExterno: Long?,
    @field:JsonProperty("TerminalExterno") val terminalExterno: String?,
    @field:JsonProperty("ProtocoloId") val protocoloId: Long?,
    @field:JsonProperty("TipoTransacao") val tipoTransacao: String?,
    @field:JsonProperty("DadosTransacao") val dadosTransacao: DadosTransacao,
    @field:JsonProperty("StatusCompensacao") val statusCompensacao: StatusCompensacao,
)

data class DadosTransacao(
    @field:JsonProperty("DataDevolucao") val dataDevolucao: String?,
    @field:JsonProperty("DataOperacao") val dataOperacao: String?,
    @field:JsonProperty("Cpfcnpj") val cpfCnpj: Long?,
    @field:JsonProperty("Agencia") val agencia: Long?,
    @field:JsonProperty("Conta") val conta: Long?,
    @field:JsonProperty("DigitoVerificador") val digitoVerificador: String?,
    @field:JsonProperty("NomeCompleto") val nomeCompleto: String?,
    @field:JsonProperty("Valor") val valor: Long?,
)

data class StatusCompensacao(
    @field:JsonProperty("Status") val status: String?,
    @field:JsonProperty("CodErro") val codErro: Int?,
    @field:JsonProperty("MensagemErro") val mensagemErro: String?,
)