package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.InternalBankAccountType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BonusCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCredit
import ai.friday.billpayment.app.integrations.CustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCreditReason
import ai.friday.billpayment.app.integrations.CustomerAccountCreditRepository
import ai.friday.billpayment.app.integrations.RefundedBillCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.RefundedSubscriptionCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.TransactionRollbackCustomerAccountCreditDetails
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.json.getObjectMapper
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val ENTITY_PREFIX = "CREDIT"

@Singleton
class CustomerAccountCreditDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<CustomerAccountCreditEntity>(cli, CustomerAccountCreditEntity::class.java)

@Singleton
class CustomerAccountCreditDbRepository(
    private val client: CustomerAccountCreditDynamoDAO,
) : CustomerAccountCreditRepository {
    private val objectMapper = getObjectMapper()

    override fun save(customerAccountCredit: CustomerAccountCredit) {
        val entity = CustomerAccountCreditEntity().apply {
            partitionKey = customerAccountCredit.accountPaymentMethodId.value
            sortKey = buildSortKey(customerAccountCredit.createdAt)
            accountPaymentMethodId = customerAccountCredit.accountPaymentMethodId.value
            index1RangeKey = buildIndex1RangeKey(customerAccountCredit.details.reason, customerAccountCredit.details.indexedDate())
            amount = customerAccountCredit.amount
            fromInternalBankAccount = customerAccountCredit.fromInternalBankAccount
            details = objectMapper.writeValueAsString(customerAccountCredit.details.toEntity())
            createdAt = customerAccountCredit.createdAt.format(dateTimeFormat)
            oldCreatedAt = ""
        }
        client.save(entity)
    }

    override fun findByCreditDate(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): List<CustomerAccountCredit> {
        return client.findByPartitionKeyAndSortKeyBetween(
            partitionKey = accountPaymentMethodId.value,
            sortKeyFrom = buildSortKey(startDate),
            sortKeyTo = buildSortKey(endDate),
        ).map { it.toDomain(objectMapper) }
    }

    override fun findByReasonAndIndexedDate(
        accountPaymentMethodId: AccountPaymentMethodId,
        reason: CustomerAccountCreditReason,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): List<CustomerAccountCredit> {
        return client.findByPartitionKeyAndSortKeyBetweenByIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = accountPaymentMethodId.value,
            sortKeyFrom = buildIndex1RangeKey(reason, startDate),
            sortKeyTo = buildIndex1RangeKey(reason, endDate),
        ).map { it.toDomain(objectMapper) }
    }

    private fun buildSortKey(date: ZonedDateTime) = "$ENTITY_PREFIX#${date.format(dateTimeFormat)}"
    private fun buildIndex1RangeKey(reason: CustomerAccountCreditReason, date: ZonedDateTime?) = "$ENTITY_PREFIX#$reason#${date?.format(dateTimeFormat).orEmpty()}"

    private fun CustomerAccountCreditDetails.indexedDate(): ZonedDateTime? = when (this) {
        is BonusCustomerAccountCreditDetails -> null
        is RefundedBillCustomerAccountCreditDetails -> originalPaidDate
        is RefundedSubscriptionCustomerAccountCreditDetails -> null
        is TransactionRollbackCustomerAccountCreditDetails -> null
    }
}

@DynamoDbBean
class CustomerAccountCreditEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var accountPaymentMethodId: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "FromInternalBankAccount")
    lateinit var fromInternalBankAccount: InternalBankAccountType

    @get:DynamoDbAttribute(value = "Details")
    lateinit var details: String

    @get:DynamoDbAttribute(value = "GSIndex1SortKey") // FIXME remover depois de rodar script
    lateinit var oldCreatedAt: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    fun toDomain(objectMapper: ObjectMapper): CustomerAccountCredit {
        val detailsData = objectMapper.readValue(details, CustomerAccountCreditDetailsEntity::class.java)
        return CustomerAccountCredit(
            accountPaymentMethodId = AccountPaymentMethodId(accountPaymentMethodId),
            amount = amount,
            fromInternalBankAccount = fromInternalBankAccount,
            details = detailsData.toDomain(),
            createdAt = ZonedDateTime.parse(
                if (::createdAt.isInitialized) {
                    createdAt
                } else {
                    oldCreatedAt
                },
                dateTimeFormat,
            ),
        )
    }
}

private fun CustomerAccountCreditDetails.toEntity() = when (this) {
    is BonusCustomerAccountCreditDetails -> BonusCustomerAccountCreditDetailsEntity(description)
    is TransactionRollbackCustomerAccountCreditDetails -> TransactionRollbackCustomerAccountCreditDetailsEntity(transactionId.value)
    is RefundedSubscriptionCustomerAccountCreditDetails -> RefundedSubscriptionCustomerAccountCreditDetailsEntity(billId.value)
    is RefundedBillCustomerAccountCreditDetails -> RefundedBillCustomerAccountCreditDetailsEntity(
        billId = billId.value,
        billPayee = billPayee,
        billDescription = billDescription,
        amountPaidWithBalance = amountPaidWithBalance,
        amountPaidWithCreditCard = amountPaidWithCreditCard,
        type = type,
        transactionId = transactionId.value,
        originalPaidDate = originalPaidDate.format(dateTimeFormat),
    )
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed interface CustomerAccountCreditDetailsEntity {
    fun toDomain(): CustomerAccountCreditDetails
}

@JsonTypeName("TRANSACTION_ROLLBACK")
data class TransactionRollbackCustomerAccountCreditDetailsEntity(
    val transactionId: String,
) : CustomerAccountCreditDetailsEntity {
    override fun toDomain() = TransactionRollbackCustomerAccountCreditDetails(
        transactionId = TransactionId(transactionId),
    )
}

@JsonTypeName("REFUNDED_BILL")
data class RefundedBillCustomerAccountCreditDetailsEntity(
    val billId: String,
    val billPayee: String = "", // FIXME remove default depois de rodar script
    val billDescription: String = "", // FIXME remove default depois de rodar script
    val amountPaidWithBalance: Long,
    val amountPaidWithCreditCard: Long,
    val type: BillType,
    val transactionId: String,
    val originalPaidDate: String,
) : CustomerAccountCreditDetailsEntity {
    override fun toDomain() = RefundedBillCustomerAccountCreditDetails(
        billId = BillId(billId),
        billPayee = billPayee,
        billDescription = billDescription,
        amountPaidWithBalance = amountPaidWithBalance,
        amountPaidWithCreditCard = amountPaidWithCreditCard,
        type = type,
        transactionId = TransactionId(transactionId),
        originalPaidDate = ZonedDateTime.parse(originalPaidDate, dateTimeFormat),
    )
}

@JsonTypeName("REFUNDED_SUBSCRIPTION")
data class RefundedSubscriptionCustomerAccountCreditDetailsEntity(
    val billId: String,
) : CustomerAccountCreditDetailsEntity {
    override fun toDomain() = RefundedSubscriptionCustomerAccountCreditDetails(
        billId = BillId(billId),
    )
}

@JsonTypeName("BONUS")
data class BonusCustomerAccountCreditDetailsEntity(
    val description: String,
) : CustomerAccountCreditDetailsEntity {
    override fun toDomain() = BonusCustomerAccountCreditDetails(
        description = description,
    )
}