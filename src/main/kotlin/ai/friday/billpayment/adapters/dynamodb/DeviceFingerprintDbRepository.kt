package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.converters.AccountIdConverter
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.fingerprint.DeviceFingerprint
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintRepository
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.fingerprint.DeviceLiveness
import ai.friday.billpayment.app.fingerprint.DeviceScreenResolution
import ai.friday.billpayment.app.fingerprint.DeviceStatus
import ai.friday.billpayment.app.fingerprint.DeviceType
import ai.friday.billpayment.app.fingerprint.MobileDeviceDetails
import ai.friday.billpayment.app.fingerprint.RegisteredDevice
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.util.UUID
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.internal.converter.attribute.ZonedDateTimeAsStringAttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val prefix = "DEVICE_FINGERPRINT#"

@Singleton
class DeviceFingerprintDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<DeviceFingerprintEntity>(cli, DeviceFingerprintEntity::class.java)

@FridayMePoupe
class DeviceFingerprintDbRepository(
    private val client: DeviceFingerprintDynamoDAO,
) : DeviceFingerprintRepository {
    override fun save(device: RegisteredDevice, accountId: AccountId) {
        client.save(device.toDeviceFingerprintEntity(accountId = accountId))
    }

    override fun getByAccountIdOrNull(accountId: AccountId, status: DeviceStatus): RegisteredDevice? {
        val entity = client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, prefix + accountId.value)
            .filter { it.deviceStatus == status }
            .maxByOrNull { it.createdAt }
        return entity?.toRegisteredDevice()
    }

    override fun getByAccountId(accountId: AccountId): List<RegisteredDevice> {
        val entity = client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, prefix + accountId.value)
        return entity.map { it.toRegisteredDevice() }
    }

    override fun getDeviceOrNull(deviceFingerprint: DeviceFingerprint, accountId: AccountId): RegisteredDevice? {
        val entity = client.findByPrimaryKey(prefix + deviceFingerprint.value, accountId.value)
        return entity?.toRegisteredDevice()
    }
}

@DynamoDbBean
class DeviceFingerprintEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // prefix + fingerprint

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // accountId

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // prefix + accountId

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String // status

    @get:DynamoDbConvertedBy(value = AccountIdConverter::class)
    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: AccountId

    @get:DynamoDbAttribute(value = "DeviceFingerprint")
    lateinit var deviceFingerprint: String

    @get:DynamoDbAttribute(value = "DeviceIds")
    lateinit var deviceIds: Map<String, String> // accountNumber -> fingerprint

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: ZonedDateTime

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: ZonedDateTime

    @get:DynamoDbAttribute(value = "DeviceStatus")
    lateinit var deviceStatus: DeviceStatus

    @get:DynamoDbAttribute(value = "DeviceType")
    lateinit var deviceType: DeviceType

    @get:DynamoDbAttribute(value = "DeviceAlias")
    lateinit var deviceAlias: String

    @get:DynamoDbAttribute(value = "DeviceDetails")
    lateinit var deviceDetails: DeviceDetailsEntity

    @get:DynamoDbAttribute(value = "LivenessId")
    var livenessId: String? = null

    @get:DynamoDbAttribute(value = "NeedEnrollment")
    var needEnrollment: Boolean? = false
}

@DynamoDbBean
class DeviceDetailsEntity {
    lateinit var type: DeviceType
    lateinit var uuid: String
    lateinit var alias: String
    lateinit var fingerprint: String
    lateinit var screenResolution: DeviceScreenResolutionEntity
    var dpi: Double = 0.0
    lateinit var manufacturer: String
    lateinit var model: String

    @get:DynamoDbAttribute(value = "rooted")
    var rooted: Boolean = false
    var storageCapacity: Int = 0
    lateinit var osId: String
    var fresh: Boolean = false
}

@DynamoDbBean
class DeviceScreenResolutionEntity {
    var width: Int = 0
    var height: Int = 0
}

private fun DeviceFingerprintEntity.toRegisteredDevice(): RegisteredDevice {
    val device = this
    return RegisteredDevice(
        status = device.deviceStatus,
        creationDate = device.createdAt,
        details = device.toDeviceDetails(),
        deviceIds = device.deviceIds.map { (accountNumber, deviceId) ->
            AccountNumber(accountNumber) to DeviceId(deviceId)
        }.toMap(),
        fingerprint = DeviceFingerprint(deviceFingerprint),
        liveness = device.livenessId?.let { DeviceLiveness(id = LivenessId(it, LivenessProvider.FACETEC), enrollment = device.needEnrollment!!) },
    )
}

private fun DeviceFingerprintEntity.toDeviceDetails(): MobileDeviceDetails {
    return MobileDeviceDetails(
        uuid = UUID.fromString(deviceDetails.uuid),
        alias = deviceAlias,
        fingerprint = deviceDetails.fingerprint,
        screenResolution = deviceDetails.screenResolution.toDeviceScreenResolution(),
        type = deviceType,
        dpi = deviceDetails.dpi,
        manufacturer = deviceDetails.manufacturer,
        model = deviceDetails.model,
        rooted = deviceDetails.rooted,
        storageCapacity = deviceDetails.storageCapacity,
        osId = deviceDetails.osId,
        fresh = deviceDetails.fresh,
    )
}

private fun DeviceScreenResolutionEntity.toDeviceScreenResolution(): DeviceScreenResolution {
    return DeviceScreenResolution(width, height)
}

private fun RegisteredDevice.toDeviceFingerprintEntity(accountId: AccountId): DeviceFingerprintEntity {
    val device = this
    return DeviceFingerprintEntity().apply {
        partitionKey = prefix + device.fingerprint.value
        scanKey = accountId.value
        index1HashKey = prefix + accountId.value
        index1RangeKey = device.status.name
        this.accountId = accountId
        this.deviceIds =
            device.deviceIds.map { (accountNumber, deviceId) -> accountNumber.fullAccountNumber to deviceId.value }
                .toMap()
        deviceAlias = device.details.alias
        createdAt = device.creationDate
        updatedAt = getZonedDateTime()
        deviceStatus = device.status
        deviceType = device.details.type
        deviceDetails = device.toDeviceDetailsEntity()
        deviceFingerprint = device.fingerprint.value
        livenessId = device.liveness?.id?.value
        needEnrollment = device.liveness?.enrollment
    }
}

private fun RegisteredDevice.toDeviceDetailsEntity(): DeviceDetailsEntity {
    return DeviceDetailsEntity().apply {
        type = details.type
        uuid = details.uuid.toString()
        alias = details.alias
        fingerprint = details.fingerprint!!
        screenResolution = details.screenResolution.toScreenResolutionEntity()
        dpi = details.dpi
        manufacturer = details.manufacturer
        model = details.model
        fresh = details.fresh
        rooted = details.rooted
        storageCapacity = details.storageCapacity
        osId = details.osId
    }
}

private fun DeviceScreenResolution.toScreenResolutionEntity(): DeviceScreenResolutionEntity {
    val deviceScreenResolution = this
    return DeviceScreenResolutionEntity().apply {
        width = deviceScreenResolution.width
        height = deviceScreenResolution.height
    }
}