package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.BankStatementMetadata
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.app.banking.OmnibusBankStatement
import ai.friday.billpayment.app.banking.OmnibusBankStatementItem
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.pix.PixStatementItem
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class InternalBankDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<BankStatementItemEntity>(cli, BankStatementItemEntity::class.java)

@Singleton
class InternalBankDBRepository(private val client: InternalBankDynamoDAO) : InternalBankRepository {

    private val bankStatementScanKeyPrefix = "STATEMENT#"

    override fun findBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        date: LocalDate,
        operationNumber: String,
    ): Either<FindError, BankStatementItem> {
        val entity = client.findByPrimaryKey(
            accountPaymentMethodId.value,
            "$bankStatementScanKeyPrefix${date.format(dateFormat)}#$operationNumber",
        )
        return entity?.mapToBankStatementItem()?.right() ?: FindError.NotFound.left()
    }

    override fun findBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        operationNumber: String,
    ): Either<FindError, BankStatementItem> {
        val items = client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = accountPaymentMethodId.value,
            sortKey = operationNumber,
        )
        val entity = items.firstOrNull() ?: return FindError.NotFound.left()
        return entity.mapToBankStatementItem().right()
    }

    override fun findBankStatementItemByEndToEnd(
        accountPaymentMethodId: AccountPaymentMethodId,
        endToEnd: EndToEnd,
    ): Either<FindError, BankStatementItem> {
        val items = client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = accountPaymentMethodId.value,
            sortKey = endToEnd.value,
        )
        val entity = items.firstOrNull() ?: return FindError.NotFound.left()
        return entity.mapToBankStatementItem().right()
    }

    fun findBankStatementItemsByEndToEnd(
        accountPaymentMethodId: AccountPaymentMethodId,
        endToEnd: EndToEnd,
    ): List<BankStatementItem> {
        val items = client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex2,
            partitionKey = accountPaymentMethodId.value,
            sortKey = endToEnd.value,
        )
        return items.map { it.mapToBankStatementItem() }
    }

    override fun save(omnibusBankStatementItem: OmnibusBankStatementItem) {
        save(
            omnibusBankStatementItem.omnibusPaymentMethodId,
            omnibusBankStatementItem.bankStatementItem,
            omnibusBankStatementItem.virtualPaymentMethodId,
        )
    }

    override fun create(internalBankStatementItem: InternalBankStatementItem) {
        val items = client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = internalBankStatementItem.accountPaymentMethodId.value,
            sortKey = internalBankStatementItem.bankStatementItem.operationNumber,
        )

        if (items.isEmpty()) {
            save(internalBankStatementItem.accountPaymentMethodId, internalBankStatementItem.bankStatementItem)
        } else {
            throw IllegalStateException("Statement already exists.")
        }
    }

    override fun update(internalBankStatementItem: InternalBankStatementItem) {
        save(internalBankStatementItem.accountPaymentMethodId, internalBankStatementItem.bankStatementItem)
    }

    override fun remove(internalBankStatementItem: InternalBankStatementItem) {
        client.delete(internalBankStatementItem.accountPaymentMethodId.value, buildScanKey(internalBankStatementItem.bankStatementItem.date, internalBankStatementItem.bankStatementItem.operationNumber))
    }

    override fun findAllBankStatementItem(accountPaymentMethodId: AccountPaymentMethodId): BankStatement {
        val list = client.findByPartitionKey(accountPaymentMethodId.value)
            .map { it.mapToBankStatementItem() }
        return BankStatement(list)
    }

    override fun findAllBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): BankStatement {
        val list = client.findByPartitionKeyAndSortKeyBetween(
            partitionKey = accountPaymentMethodId.value,
            sortKeyFrom = buildScanKey(date = startDate),
            sortKeyTo = buildScanKey(date = endDate),
        ).map { it.mapToBankStatementItem() }
        return BankStatement(list)
    }

    override fun findAllBankStatementCredits(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BankStatementItem> {
        val initialScanKey = buildScanKey(startDate)
        val finalScanKey = buildScanKey(endDate.plusDays(1))
        return client.findByPartitionKeyAndSortKeyBetween(
            partitionKey = accountPaymentMethodId.value,
            sortKeyFrom = initialScanKey,
            sortKeyTo = finalScanKey,
        )
            .map { it.mapToBankStatementItem() }.filter { it.flow == BankStatementItemFlow.CREDIT }
    }

    override fun findAllOmnibusBankStatementItem(accountPaymentMethodId: AccountPaymentMethodId): OmnibusBankStatement {
        val list = client.findByPartitionKey(accountPaymentMethodId.value)
            .map { it.mapToOmnibusBankStatementItem() }
        return OmnibusBankStatement(list)
    }

    private fun save(
        accountPaymentMethodId: AccountPaymentMethodId,
        item: BankStatementItem,
        targetAccountPaymentMethodId: AccountPaymentMethodId? = null,
    ) {
        val entity = BankStatementItemEntity().apply {
            primaryKey = accountPaymentMethodId.value
            scanKey = buildScanKey(item.date, item.operationNumber)
            flow = item.flow
            type = item.type
            amount = item.amount
            operationNumber = item.operationNumber
            temporaryOperationNumber = item.isTemporaryOperationNumber
            endToEnd = item.endToEnd?.value
            date = item.date.format(dateFormat)
            counterpartName = item.counterpartName
            counterpartDocument = item.counterpartDocument
            counterpartBankName = item.counterpartBankName
            ref = item.ref.orEmpty()
            target = targetAccountPaymentMethodId?.value.orEmpty()
            description = item.description
            documentNumber = item.documentNumber
            index1HashKey = accountPaymentMethodId.value
            index1RangeKey = item.operationNumber
            index2HashKey = accountPaymentMethodId.value
            index2RangeKey = item.endToEnd?.value
            metadata = item.metadata.toEntity()
            notificatedAt = item.notificatedAt?.format(dateTimeFormat)
        }

        client.save(entity)
    }

    private fun buildScanKey(
        date: LocalDate,
        operationNumber: String? = "",
    ): String {
        return bankStatementScanKeyPrefix + date.format(dateFormat) + "#" + operationNumber
    }
}

@DynamoDbBean
class BankStatementItemEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // PAYMENT-METHOD-ID

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String // OPERATION-NUMBER

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2PrimaryKey")
    lateinit var index2HashKey: String // PAYMENT-METHOD-ID

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_2])
    @get:DynamoDbAttribute(value = "GSIndex2ScanKey")
    var index2RangeKey: String? = null // END-TO-END

    @get:DynamoDbAttribute(value = "Flow")
    lateinit var flow: BankStatementItemFlow

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: BankStatementItemType

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "OperationNumber")
    lateinit var operationNumber: String

    @get:DynamoDbAttribute(value = "temporaryOperationNumber")
    var temporaryOperationNumber: Boolean? = null

    @get:DynamoDbAttribute(value = "endToEnd")
    var endToEnd: String? = null

    @get:DynamoDbAttribute(value = "Date")
    lateinit var date: String

    @get:DynamoDbAttribute(value = "CounterpartName")
    var counterpartName: String = ""

    @get:DynamoDbAttribute(value = "CounterpartDocument")
    var counterpartDocument: String = ""

    @get:DynamoDbAttribute(value = "CounterpartBankName")
    var counterpartBankName: String? = null

    @get:DynamoDbAttribute(value = "DocumentNumber")
    var documentNumber: String = ""

    @get:DynamoDbAttribute(value = "Ref")
    var ref: String? = null

    @get:DynamoDbAttribute(value = "Description")
    var description: String = ""

    @get:DynamoDbAttribute(value = "Target")
    var target: String? = null

    @get:DynamoDbAttribute(value = "BankStatementMetadata")
    var metadata: BankStatementMetadataEntity? = null

    @get:DynamoDbAttribute(value = "NotificatedAt")
    var notificatedAt: String? = null
}

fun BankStatementMetadata?.toEntity(): BankStatementMetadataEntity? {
    return this?.let {
        BankStatementMetadataEntity().apply {
            virtualAmount = it.virtualAmount
        }
    }
}

@DynamoDbBean
class BankStatementMetadataEntity {
    var virtualAmount: Long? = null

    fun toDomainObject(): BankStatementMetadata {
        return BankStatementMetadata(
            virtualAmount = this.virtualAmount,
        )
    }
}

fun BankStatementItemEntity.mapToBankStatementItem(): BankStatementItem {
    return if (type == BankStatementItemType.PIX || type == BankStatementItemType.DEVOLUCAO_PIX) {
        PixStatementItem(
            date = LocalDate.parse(date, dateFormat),
            amount = amount,
            flow = flow,
            description = description,
            counterpartDocument = counterpartDocument,
            counterpartName = counterpartName,
            counterpartBankName = counterpartBankName,
            documentNumber = documentNumber,
            operationNumber = operationNumber,
            type = type,
            ref = ref?.takeIf { it.isNotEmpty() },
            metadata = metadata?.toDomainObject(),
            notificatedAt = notificatedAt?.let {
                ZonedDateTime.parse(it, dateTimeFormat)
            },
            transactionId = ref?.takeIf { it.isNotEmpty() }?.let { QrCodeTransactionId.from(it) },
            endToEnd = endToEnd?.let { EndToEnd(endToEnd!!) },
        )
    } else {
        DefaultBankStatementItem(
            date = LocalDate.parse(date, dateFormat),
            amount = amount,
            flow = flow,
            description = description,
            counterpartDocument = counterpartDocument,
            counterpartName = counterpartName,
            counterpartBankName = counterpartBankName,
            documentNumber = documentNumber,
            operationNumber = operationNumber,
            type = type,
            ref = ref?.takeIf { it.isNotEmpty() },
            metadata = metadata?.toDomainObject(),
            notificatedAt = notificatedAt?.let {
                ZonedDateTime.parse(it, dateTimeFormat)
            },
        )
    }
}

fun BankStatementItemEntity.mapToOmnibusBankStatementItem() = OmnibusBankStatementItem(
    bankStatementItem = mapToBankStatementItem(),
    omnibusPaymentMethodId = AccountPaymentMethodId(primaryKey),
    virtualPaymentMethodId = target?.let { AccountPaymentMethodId(it) },
)