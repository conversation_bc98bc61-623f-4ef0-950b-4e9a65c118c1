package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.integrations.CheckRegisterDocumentAllowedResult
import ai.friday.billpayment.app.integrations.RegisterAllowedDocumentNumberRepository
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val REGISTER_ALLOWED_DOCUMENTS = "REGISTER_ALLOWED_DOCUMENTS"
private const val ENABLED_SCAN_KEY = "ENABLED"

@Singleton
class RegisterAllowedDocumentNumberDynamoDao(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<RegisterAllowedDocumentNumberEntity>(cli, RegisterAllowedDocumentNumberEntity::class.java)

@Singleton
class RegisterAllowedDocumentNumberDbRepository(private val dao: RegisterAllowedDocumentNumberDynamoDao) : RegisterAllowedDocumentNumberRepository {
    override fun toggle(enabled: Boolean): Result<Unit> = kotlin.runCatching {
        val entity = RegisterAllowedDocumentNumberEntity().apply {
            this.primaryKey = REGISTER_ALLOWED_DOCUMENTS
            this.scanKey = ENABLED_SCAN_KEY
            this.value = getObjectMapper().writeValueAsString(enabled)
        }

        dao.save(entity)
    }

    override fun check(document: Document): Result<CheckRegisterDocumentAllowedResult> = runCatching {
        if (!checkIsEnabled()) {
            CheckRegisterDocumentAllowedResult.NoRestrictionApplies
        } else {
            if (dao.findByPrimaryKey(REGISTER_ALLOWED_DOCUMENTS, document.value) != null) {
                CheckRegisterDocumentAllowedResult.Allowed
            } else {
                CheckRegisterDocumentAllowedResult.NotAllowed
            }
        }
    }

    override fun add(documents: List<Document>): Result<Unit> = runCatching {
        documents.forEach {
            val entity = RegisterAllowedDocumentNumberEntity().apply {
                this.primaryKey = REGISTER_ALLOWED_DOCUMENTS
                this.scanKey = it.toScanKey()
                this.value = it.value
            }

            dao.save(entity)
        }
    }

    override fun remove(documents: List<Document>): Result<Unit> = runCatching {
        documents.forEach {
            dao.delete(REGISTER_ALLOWED_DOCUMENTS, it.toScanKey())
        }
    }

    private fun checkIsEnabled(): Boolean {
        return dao.findByPrimaryKey(REGISTER_ALLOWED_DOCUMENTS, ENABLED_SCAN_KEY)
            ?.value?.let { parseObjectFrom(it) }
            ?: false
    }

    private fun Document.toScanKey() = "DOCUMENT#${this.value}"
}

@DynamoDbBean
class RegisterAllowedDocumentNumberEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute(value = "Value")
    lateinit var value: String
}