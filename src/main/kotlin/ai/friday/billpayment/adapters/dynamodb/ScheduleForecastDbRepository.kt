package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.bill.iso
import ai.friday.billpayment.app.integrations.ScheduleForecastRepository
import ai.friday.billpayment.app.integrations.ScheduleForecastResult
import com.amazonaws.services.dynamodbv2.model.AttributeAction
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate
import com.amazonaws.services.dynamodbv2.model.ReturnValue
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.LocalDate
import kotlin.math.absoluteValue
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val FORECAST_PREFIX = "SCHEDULE_FORECAST"
private const val PROVISIONED_AMOUNT_FIELD_NAME = "ProvisionedAmount"
private const val SCHEDULED_BILLS_FIELD_NAME = "ScheduledBills"
private const val PAID_BILLS_FIELD_NAME = "PaidBills"
private const val PAID_AMOUNT_FIELD_NAME = "PaidAmount"

@Singleton
class ScheduleForecastDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<ScheduleForecastEntity>(cli, ScheduleForecastEntity::class.java)

@Singleton
class ScheduleForecastDbRepository(
    private val db: DynamoDbDAO,
    private val scheduleForecastDAO: ScheduleForecastDynamoDAO,
    @param:Property(name = "dynamodb.billPaymentTableName") private val tableName: String,
) : ScheduleForecastRepository {
    override fun processSchedule(date: LocalDate, amount: Long): ScheduleForecastResult.Item {
        val billAmount = amount.absoluteValue

        val updates = mapOf(
            SCHEDULED_BILLS_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("1"), AttributeAction.ADD),
            PROVISIONED_AMOUNT_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("$billAmount"), AttributeAction.ADD),
        )

        return updateItem(date, updates)
    }

    override fun processCancelment(date: LocalDate, amount: Long): ScheduleForecastResult.Item {
        val billAmount = -1 * amount.absoluteValue

        val updates = mapOf(
            SCHEDULED_BILLS_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("-1"), AttributeAction.ADD),
            PROVISIONED_AMOUNT_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("$billAmount"), AttributeAction.ADD),
        )

        return updateItem(date, updates)
    }

    override fun processPayment(date: LocalDate, amount: Long): ScheduleForecastResult.Item {
        val billAmountInc = -1 * amount.absoluteValue
        val paidAmountInc = amount.absoluteValue

        val updates = mapOf(
            SCHEDULED_BILLS_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("-1"), AttributeAction.ADD),
            PROVISIONED_AMOUNT_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("$billAmountInc"), AttributeAction.ADD),
            PAID_BILLS_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("1"), AttributeAction.ADD),
            PAID_AMOUNT_FIELD_NAME to AttributeValueUpdate(AttributeValue().withN("$paidAmountInc"), AttributeAction.ADD),
        )

        return updateItem(date, updates)
    }

    private fun updateItem(date: LocalDate, updates: Map<String, AttributeValueUpdate>): ScheduleForecastResult.Item {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(FORECAST_PREFIX),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(date.iso()),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(tableName)
            .withKey(key)
            .withReturnValues(ReturnValue.ALL_NEW)
            .withAttributeUpdates(updates)

        db.updateItem(updateRequest).run {
            return ScheduleForecastResult.Item(
                provisionedAmount = attributes[PROVISIONED_AMOUNT_FIELD_NAME]?.n?.toLong() ?: 0L,
                scheduledBills = attributes[SCHEDULED_BILLS_FIELD_NAME]?.n?.toLong() ?: 0L,
                paidBills = attributes[PAID_BILLS_FIELD_NAME]?.n?.toLong() ?: 0L,
                paidAmount = attributes[PAID_AMOUNT_FIELD_NAME]?.n?.toLong() ?: 0L,
            )
        }
    }

    fun save(scheduleForecast: ScheduleForecastEntity) {
        val newEntity = ScheduleForecastEntity().apply {
            primaryKey = scheduleForecast.primaryKey
            scanKey = scheduleForecast.scanKey
            provisionedAmount = scheduleForecast.provisionedAmount
            scheduledBills = scheduleForecast.scheduledBills
            paidBills = scheduleForecast.paidBills
            paidAmount = scheduleForecast.paidAmount
        }
        scheduleForecastDAO.save(newEntity)
    }

    override fun find(date: LocalDate): ScheduleForecastResult =
        scheduleForecastDAO.findByPrimaryKey(FORECAST_PREFIX, date.iso())?.let {
            ScheduleForecastResult.Item(
                provisionedAmount = it.provisionedAmount,
                scheduledBills = it.scheduledBills,
                paidBills = it.paidBills,
                paidAmount = it.paidAmount,
            )
        } ?: ScheduleForecastResult.NotFound
}

@DynamoDbBean
class ScheduleForecastEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // SCHEDULE_FORECAST

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // LOCAL DATE

    @get:DynamoDbAttribute(value = PROVISIONED_AMOUNT_FIELD_NAME)
    var provisionedAmount: Long = 0

    @get:DynamoDbAttribute(value = SCHEDULED_BILLS_FIELD_NAME)
    var scheduledBills: Long = 0

    @get:DynamoDbAttribute(value = PAID_BILLS_FIELD_NAME)
    var paidBills: Long = 0

    @get:DynamoDbAttribute(value = PAID_AMOUNT_FIELD_NAME)
    var paidAmount: Long = 0
}