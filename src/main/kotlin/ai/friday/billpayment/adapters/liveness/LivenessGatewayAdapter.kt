package ai.friday.billpayment.adapters.liveness

import ai.friday.billpayment.ByteWrapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofResult
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofStatus
import ai.friday.billpayment.app.documentscan.DocumentScanFaceResult
import ai.friday.billpayment.app.documentscan.DocumentScanFaceStatus
import ai.friday.billpayment.app.documentscan.DocumentScanId
import ai.friday.billpayment.app.documentscan.DocumentScanImage
import ai.friday.billpayment.app.documentscan.DocumentScanOcrResult
import ai.friday.billpayment.app.documentscan.DocumentScanPayloadData
import ai.friday.billpayment.app.documentscan.DocumentScanRepository
import ai.friday.billpayment.app.documentscan.DocumentScanResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextStatus
import ai.friday.billpayment.app.documentscan.FacetecDocumentScanPayloadData
import ai.friday.billpayment.app.documentscan.GetDocumentScanResultError
import ai.friday.billpayment.app.documentscan.GetImageError
import ai.friday.billpayment.app.documentscan.ImageContent
import ai.friday.billpayment.app.documentscan.UpsertDocumentScanIdError
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessMatchVerify
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.net.URI
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.liveness")
class LivenessConfiguration {
    lateinit var host: String
    lateinit var user: String
    lateinit var password: String
    lateinit var enrollmentPath: String
    lateinit var matchPath: String
    lateinit var verifyMatchPath: String
    lateinit var selfiePath: String
    lateinit var duplicationVerificationPath: String
    lateinit var hasCompletedEnrollmentPath: String
    lateinit var markAsFraudPath: String
}

@ConfigurationProperties("integrations.documentscan")
class DocumentScanConfiguration {
    lateinit var host: String
    lateinit var user: String
    lateinit var password: String
    lateinit var getImagePath: String
    lateinit var getScanResultPath: String
    lateinit var upsertDocumentScanPath: String
}

@ConfigurationProperties("integrations.livenessCaf")
class LivenessCafConfiguration {
    lateinit var host: String
    lateinit var user: String
    lateinit var password: String
    lateinit var cafLivenessPath: String
    lateinit var cafDocumentDetectorPath: String
}

@Singleton
@Named("FACETEC")
open class LivenessGatewayAdapter(
    private val livenessConfiguration: LivenessConfiguration,
    private val documentScanConfiguration: DocumentScanConfiguration,
    private val cafConfiguration: LivenessCafConfiguration,
) : LivenessService, DocumentScanRepository {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val httpClient = RxHttpClient.create(URI(livenessConfiguration.host).toURL())

    override fun enroll(accountId: AccountId): Either<LivenessErrors, LivenessId> {
        val marker = Markers.append("accountId", accountId.value)
        val logName = "LivenessAdapter#enroll"

        try {
            val requestBody = CheckLivenessRequestTO(externalId = accountId.value)
            marker.andAppend("requestBody", requestBody)

            val httpRequest =
                HttpRequest.POST(livenessConfiguration.enrollmentPath, requestBody)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(LivenessEnrollmentResponseTO::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), logName)
            return LivenessId(response.livenessId, LivenessProvider.FACETEC).right()
        } catch (e: HttpClientResponseException) {
            marker.andAppend("status", e.status)
            return if (e.status == HttpStatus.CONFLICT) {
                logger.warn(marker, logName)
                LivenessErrors.EnrollmentUnavailable.left()
            } else {
                logger.error(marker, logName, e)
                LivenessErrors.Error(e).left()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessErrors.Error(e).left()
        }
    }

    override fun match(accountId: AccountId): Either<LivenessErrors, LivenessId> {
        val marker = Markers.append("accountId", accountId.value)
        val logName = "LivenessAdapter#match"

        try {
            val requestBody = CheckLivenessRequestTO(externalId = accountId.value)
            marker.andAppend("requestBody", requestBody)

            val httpRequest =
                HttpRequest.POST(livenessConfiguration.matchPath, requestBody)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(LivenessEnrollmentResponseTO::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), logName)
            return LivenessId(response.livenessId, LivenessProvider.FACETEC).right()
        } catch (e: HttpClientResponseException) {
            marker.andAppend("status", e.status)
            return if (e.status == HttpStatus.BAD_REQUEST) {
                logger.warn(marker, logName)
                LivenessErrors.MatchUnavailable.left()
            } else {
                logger.error(marker, logName, e)
                LivenessErrors.Error(e).left()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessErrors.Error(e).left()
        }
    }

    override fun retrieveEnrollmentSelfie(livenessId: LivenessId): Either<LivenessSelfieError, ByteArray> {
        val marker = Markers.append("livenessId", livenessId.value)
        val logName = "LivenessAdapter#retrieveEnrollmentSelfie"

        val requestMap = mutableMapOf<String, Any>("livenessId" to livenessId.value)
        val uri = UriBuilder.of(livenessConfiguration.selfiePath).expand(requestMap).toString()
        marker.andAppend("uri", uri)
        try {
            val httpRequest =
                HttpRequest.GET<AuditImageTO>(uri)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(AuditImageTO::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("responseLivenessId", response.livenessId), logName)
            return ByteWrapper.ofBase64(response.auditImage).bytes.right()
        } catch (e: HttpClientResponseException) {
            marker.andAppend("status", e.status)
            return if (e.status == HttpStatus.BAD_REQUEST) {
                logger.warn(marker, logName)
                LivenessSelfieError.Unavailable.left()
            } else {
                logger.error(marker, logName, e)
                LivenessSelfieError.Error(e).left()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessSelfieError.Error(e).left()
        }
    }

    override fun verifyDuplication(accountId: AccountId): Either<LivenessErrors, LivenessEnrollmentVerification> {
        val marker = Markers.append("accountId", accountId.value)
        val logName = "LivenessAdapter#verifyDuplication"

        val uri = UriBuilder.of(livenessConfiguration.duplicationVerificationPath)
            .expand(mutableMapOf("externalId" to accountId.value)).toString()

        marker.andAppend("uri", uri)
        try {
            val httpRequest =
                HttpRequest.GET<EnrollmentVerificationTO>(uri)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(EnrollmentVerificationTO::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(
                marker.andAppend("duplications", response.duplications)
                    .andAppend("fraudIndications", response.fraudIndications),
                logName,
            )
            return LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(
                    response.duplications?.map {
                        AccountId(
                            it,
                        )
                    },
                ),
                fraudIndications = LivenessEnrollmentVerification.Result.create(
                    response.fraudIndications?.map {
                        AccountId(
                            it,
                        )
                    },
                ),
            ).right()
        } catch (e: HttpClientResponseException) {
            marker.andAppend("status", e.status)
            return if (e.status == HttpStatus.BAD_REQUEST) {
                logger.warn(marker, logName)
                LivenessErrors.DuplicationCheckUnavailable.left()
            } else {
                logger.error(marker, logName, e)
                return LivenessErrors.Error(e).left()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessErrors.Error(e).left()
        }
    }

    override fun verifyMatch(livenessId: LivenessId): Either<LivenessErrors, LivenessMatchVerify> {
        val marker = Markers.append("livenessId", livenessId.value)
        val logName = "LivenessAdapter#verifyMatch"
        val uri =
            UriBuilder.of(livenessConfiguration.verifyMatchPath).expand(mutableMapOf("livenessId" to livenessId.value))
        try {
            val httpRequest =
                HttpRequest.GET<Unit>(uri)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(MatchVerificationResponseTO::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), logName)
            return response.toLivenessMatchVerify().right()
        } catch (e: HttpClientResponseException) {
            marker.andAppend("status", e.status)
            return if (e.status == HttpStatus.BAD_REQUEST) {
                logger.warn(marker, logName)
                LivenessErrors.MatchUnavailable.left()
            } else {
                logger.error(marker, logName, e)
                LivenessErrors.Error(e).left()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessErrors.Error(e).left()
        }
    }

    override fun markAsFraud(accountId: AccountId): Either<LivenessErrors, Unit> {
        val marker = Markers.append("accountId", accountId.value)
        val logName = "LivenessAdapter#markAsFraud"

        try {
            val requestBody = CheckLivenessRequestTO(externalId = accountId.value)
            marker.andAppend("requestBody", requestBody)

            val httpRequest =
                HttpRequest.POST(livenessConfiguration.markAsFraudPath, requestBody)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.exchange(
                httpRequest,
                Unit::class.java,
            )

            val response = call.firstOrError().blockingGet()

            if (response.status != HttpStatus.NO_CONTENT) {
                throw HttpClientResponseException("invalid response status, expected 204", response)
            }
            logger.info(marker.andAppend("response", response), logName)
            return Unit.right()
        } catch (e: HttpClientResponseException) {
            marker.andAppend("status", e.status)
            return if (e.status == HttpStatus.NOT_FOUND) {
                logger.warn(marker, logName)
                LivenessErrors.AccountNotFound.left()
            } else {
                logger.error(marker, logName, e)
                LivenessErrors.Error(e).left()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessErrors.Error(e).left()
        }
    }

    override fun hasCompletedEnrollment(accountId: AccountId): Either<LivenessErrors, Boolean> {
        val marker = Markers.append("accountId", accountId.value)
        val logName = "LivenessAdapter#hasCompletedEnrollment"
        val uri =
            UriBuilder.of(livenessConfiguration.hasCompletedEnrollmentPath)
                .expand(mutableMapOf("externalId" to accountId.value))
        try {
            val httpRequest =
                HttpRequest.GET<Unit>(uri)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        livenessConfiguration.user,
                        livenessConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(HasCompletedLivenessTO::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), logName)
            return response.hasCompletedLiveness.right()
        } catch (e: HttpClientResponseException) {
            logger.error(
                marker.andAppend("status", e.status).andAppend("response", e.response.body()),
                logName,
                e,
            )
            return LivenessErrors.Error(e).left()
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return LivenessErrors.Error(e).left()
        }
    }

    override fun upsertDocumentScanId(payload: DocumentScanPayloadData): Either<UpsertDocumentScanIdError, DocumentScan> {
        val fixedPayload = payload as FacetecDocumentScanPayloadData
        val accountId = fixedPayload.accountId
        val marker = Markers.append("accountId", accountId.value)
        val logName = "DocumentScanRepository#upsertDocumentScanId"
        val uri =
            UriBuilder.of(documentScanConfiguration.upsertDocumentScanPath).build()

        try {
            val httpRequest =
                HttpRequest.POST(uri, DocumentScanRequestTO(externalId = accountId.value))
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        documentScanConfiguration.user,
                        documentScanConfiguration.password,
                    )

            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(DocumentScanResponse::class.java),
                Argument.STRING,
            )

            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), logName)
            return DocumentScan(documentType = fixedPayload.documentType, documentScanId = DocumentScanId(response.documentScanId, provider = LivenessProvider.FACETEC, referenceToken = null, documentscopyTransactionId = null)).right()
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return UpsertDocumentScanIdError.Unexpected(e).left()
        }
    }

    override fun getResult(id: DocumentScanId): Either<GetDocumentScanResultError, DocumentScanResult> {
        val markers = Markers.append("documentScanId", id.value)
        val logName = "DocumentScanRepository#getResult"

        try {
            val uri = UriBuilder.of(documentScanConfiguration.getScanResultPath)
                .expand(mutableMapOf("documentScanId" to id.value))

            val request =
                HttpRequest.GET<Unit>(uri)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(documentScanConfiguration.user, documentScanConfiguration.password)

            val response = httpClient.exchange(request).firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)

            return when (response.status) {
                HttpStatus.OK -> response.getBody(DocumentScanResultTO::class.java).get().toDomain().right()
                else -> throw Exception("Unexpected response code: ${response.status}")
            }
        } catch (e: HttpClientResponseException) {
            when (e.status) {
                HttpStatus.CONFLICT -> return GetDocumentScanResultError.Incomplete(id).left()
                else -> {
                    logger.warn(markers.andAppend("status", e.status).andAppend("response", e.response.body()), logName)
                    return GetDocumentScanResultError.Unexpected(e).left()
                }
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return GetDocumentScanResultError.Unexpected(e).left()
        }
    }

    override fun getImage(id: DocumentScanId): Either<GetImageError, DocumentScanImage> {
        val markers = Markers.append("documentScanId", id.value)
        val logName = "DocumentScanRepository#getImage"

        try {
            val uri = UriBuilder.of(documentScanConfiguration.getImagePath)
                .expand(mutableMapOf("documentScanId" to id.value))

            val request =
                HttpRequest.GET<Unit>(uri)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(documentScanConfiguration.user, documentScanConfiguration.password)

            val response = httpClient.exchange(request).firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)

            return when (response.status) {
                HttpStatus.OK -> response.getBody(DocumentScanImageTO::class.java).get().toDomain().right()
                else -> throw Exception("Unexpected response code: ${response.status}")
            }
        } catch (e: HttpClientResponseException) {
            when (e.status) {
                HttpStatus.CONFLICT -> return GetImageError.Incomplete(id).left()
                else -> {
                    logger.warn(markers.andAppend("status", e.status).andAppend("response", e.response.body()), logName)
                    return GetImageError.Unexpected(e).left()
                }
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return GetImageError.Unexpected(e).left()
        }
    }

    fun createLiveness(jwt: String, accountId: AccountId): Either<Exception, Unit> {
        val marker = Markers.append("jwt", jwt)
        val logName = "LivenessGatewayAdapter#createLiveness"

        try {
            val requestBody = CafLivenessRequestTO(jwt = jwt, accountId = accountId.value)
            marker.andAppend("requestBody", requestBody)

            val httpRequest =
                HttpRequest.POST(cafConfiguration.cafLivenessPath, requestBody)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        cafConfiguration.user,
                        cafConfiguration.password,
                    )

            val response = httpClient.exchange(httpRequest).firstOrError().blockingGet()
            logger.info(marker.andAppend("status", response.status), logName)

            return Unit.right()
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return e.left()
        }
    }

    fun createDocument(jwt: String, accountId: AccountId): Either<Exception, Unit> {
        val marker = Markers.append("jwt", jwt)
        val logName = "LivenessGatewayAdapter#createDocument"

        try {
            val requestBody = CafDocumentRequestTO(jwt = jwt, accountId = accountId.value)
            marker.andAppend("requestBody", requestBody)

            val httpRequest =
                HttpRequest.POST(cafConfiguration.cafDocumentDetectorPath, requestBody)
                    .contentType(MediaType.APPLICATION_JSON)
                    .basicAuth(
                        cafConfiguration.user,
                        cafConfiguration.password,
                    )

            val response = httpClient.exchange(httpRequest).firstOrError().blockingGet()
            logger.info(marker.andAppend("status", response.status), logName)

            return Unit.right()
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return e.left()
        }
    }
}

private data class CheckLivenessRequestTO(val externalId: String)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class LivenessEnrollmentResponseTO(val livenessId: String)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class MatchVerificationResponseTO(
    val livenessId: String,
    val externalId: String,
    val match: Boolean,
    val attempt: Int,
) {
    fun toLivenessMatchVerify() = LivenessMatchVerify(
        livenessId = LivenessId(value = livenessId, provider = LivenessProvider.FACETEC),
        accountId = AccountId(externalId),
        match = this.match,
        attempt = this.attempt,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
private data class AuditImageTO(
    val livenessId: String,
    val auditImage: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EnrollmentVerificationTO(
    val duplications: List<String>?,
    val fraudIndications: List<String>?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class HasCompletedLivenessTO(
    val hasCompletedLiveness: Boolean,
)

private data class DocumentScanRequestTO(val externalId: String)

private data class DocumentScanResponse(val documentScanId: String)

private data class CafLivenessRequestTO(val jwt: String, val accountId: String)

private data class CafDocumentRequestTO(val jwt: String, val accountId: String)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class DocumentScanResultTO(
    val spoof: String,
    val face: String,
    val text: String,
    val ocr: Boolean,
) {
    fun toDomain(): DocumentScanResult {
        return DocumentScanResult(
            digitalSpoof = DocumentScanDigitalSpoofResult(
                status = when (spoof) {
                    "LIKELY_PHYSICAL_ID" -> DocumentScanDigitalSpoofStatus.LIKELY_PHYSICAL_ID
                    "COULD_NOT_CONFIDENTLY_DETERMINE_PHYSICAL_ID_USER_NEEDS_TO_RETRY" -> DocumentScanDigitalSpoofStatus.CANNOT_CONFIRM_PHYSICAL_ID
                    else -> throw Exception("Unexpected digital spoof status: $spoof")
                },
            ),
            face = when (face) {
                "NOT_AVAILABLE" -> DocumentScanFaceResult.NotFound
                else -> DocumentScanFaceResult.Found(
                    status = when (face) {
                        "LIKELY_ORIGINAL_FACE" -> DocumentScanFaceStatus.LIKELY_ORIGINAL_FACE
                        "CANNOT_CONFIRM_ID_IS_AUTHENTIC" -> DocumentScanFaceStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC
                        "OCR_TEMPLATE_DOES_NOT_SUPPORT_DETECTION" -> DocumentScanFaceStatus.TEMPLATE_DOES_NOT_SUPPORT_DETECTION
                        else -> throw Exception("Unexpected face status: $face")
                    },
                )
            },
            text = when (text) {
                "NOT_AVAILABLE" -> DocumentScanTextResult.NotPerformed
                else -> DocumentScanTextResult.Performed(
                    status = when (text) {
                        "LIKELY_ORIGINAL_TEXT" -> DocumentScanTextStatus.LIKELY_ORIGINAL_TEXT
                        "CANNOT_CONFIRM_ID_IS_AUTHENTIC" -> DocumentScanTextStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC
                        else -> throw Exception("Unexpected text status: $text")
                    },
                )
            },
            ocr = if (ocr) DocumentScanOcrResult.Matched else DocumentScanOcrResult.NotMatched,
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
private data class DocumentScanImageTO(
    val frontImage: String,
    val backImage: String?,
) {
    fun toDomain(): DocumentScanImage {
        return if (backImage == null) {
            DocumentScanImage.SingleSided(content = URI(frontImage).toImageContent())
        } else {
            DocumentScanImage.DoubleSided(front = URI(frontImage).toImageContent(), back = URI(backImage).toImageContent())
        }
    }
}

private fun URI.toImageContent(): ImageContent {
    val url = this.toURL()
    return ImageContent(
        content = url.readBytes(),
        extension = url.path.substringAfterLast('.', ""),
    )
}