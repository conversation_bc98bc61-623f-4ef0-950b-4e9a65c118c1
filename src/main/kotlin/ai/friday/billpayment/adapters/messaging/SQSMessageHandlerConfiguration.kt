package ai.friday.billpayment.adapters.messaging

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("sqs")
data class SQSMessageHandlerConfiguration
@ConfigurationInject constructor(
    val billPaymentSchedulingQueueName: String,
    val emailQueueName: String,
    val settlementResponseQueueName: String,
    val rollbackTransactionQueueName: String,
    val invalidateBankAccountQueueName: String,
    val bankAccountDepositQueueName: String,
    val validateBoletoJaBaixadoQueueName: String,
    val registerPixKeyQueueName: String,
    val incomeReport: String,
    override val sqsWaitTime: Int,
    override val maxReceiveCount: Int,
    override val sqsCoolDownTime: Int,
    val billEventNotificationQueueName: String,
    val trackableBillQueueName: String,
    val billReceiptGeneratorQueueName: String,
    val updateScheduledBillQueueName: String,
    val settlementFundsTransferQueueName: String,
    val walletEventsQueueName: String,
    val ddaQueueName: String,
    val billPaymentScheduledQueueName: String,
    val userJourneyQueueName: String,
    val ddaBatchAddOrdersQueueName: String,
    val ddaBatchMigrationOrdersQueueName: String,
    val ddaFullImportQueueName: String,
    val billComingDueQueueName: String,
    val simpleSignUpQueueName: String,
    override val dlqArn: String,
    override val visibilityTimeout: Int,
    override val maxNumberOfMessages: Int,
    override val autoScaleWorkersInParallel: Boolean,
    val ddaBills: String,
    val revenueCatQueueName: String,
    val billPaidActivityQueueName: String,
    val subscriptionBillPaidQueueName: String,
    val utilityFlowResponseQueueName: String,
    val statementQueueName: String,
    val summaryQueueName: String,
    val addBillFromEmailQueueName: String,
    val billCategorySuggestionQueueName: String,
    val setBillCategoryQueueName: String,
    val createDefaultCategories: String,
    val notifyReminderQueueName: String,
    val notifyExpiredReminderQueueName: String,
    val updateConcessionariaStatusQueueName: String,
    val vehicleDebtsQueueName: String,
    val vehicleDebtsEnrichmentQueueName: String,
    val anonymousIdInAppSubscriptionQueueName: String,
    val faceRegistrationQueueName: String,
) : ParallelMessageHandlerConfiguration

interface MessageHandlerConfiguration {
    val sqsWaitTime: Int
    val sqsCoolDownTime: Int
    val dlqArn: String
    val visibilityTimeout: Int
    val maxNumberOfMessages: Int
    val maxReceiveCount: Int
}

interface ParallelMessageHandlerConfiguration : MessageHandlerConfiguration {
    val autoScaleWorkersInParallel: Boolean
}