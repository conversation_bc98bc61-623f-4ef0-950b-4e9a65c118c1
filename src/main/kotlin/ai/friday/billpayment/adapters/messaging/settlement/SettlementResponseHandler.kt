package ai.friday.billpayment.adapters.messaging.settlement

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SettlementResponseHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val billPaymentService: BillPaymentService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.settlementResponseQueueName,
) {
    private val logger = LoggerFactory.getLogger(SettlementResponseHandler::class.java)

    override fun handleMessage(m: Message): SQSHandlerResponse {
        val settlementResponse = parseObjectFrom<SettlementClientResponseTO>(m.body())
        val markers = append("settlementResponse", settlementResponse)

        val result = when (settlementResponse) {
            is BadRequestSettlementClientResponseTO -> BoletoSettlementResult(
                gateway = FinancialServiceGateway.FRIDAY,
                status = BoletoSettlementStatus.UNAUTHORIZED,
                bankTransactionId = settlementResponse.transactionId,
                externalNsu = 0,
                externalTerminal = "",
                errorCode = BoletoSettlementStatus.UNAUTHORIZED.name,
                errorDescription = settlementResponse.errorMessage,
            )

            is FailedSettlementClientResponseTO -> handleFailedSettlement(settlementResponse)

            is PaidSettlementClientResponseTO -> BoletoSettlementResult(
                gateway = FinancialServiceGateway.FRIDAY,
                status = BoletoSettlementStatus.CONFIRMED,
                bankTransactionId = settlementResponse.transactionId,
                externalNsu = 0,
                externalTerminal = "",
                errorCode = "",
                errorDescription = null,
                authentication = settlementResponse.authorization,
                paymentPartnerName = settlementResponse.settlementFinancialInstitution,
                finalPartnerName = settlementResponse.financialServiceGateway,
            )
        }
        markers.andAppend("result", result)

        billPaymentService.continueSettlement(
            transactionId = TransactionId(value = settlementResponse.transactionId),
            settlementOperation = result,
        )

        logger.info(markers, "SettlementResponseHandler#handleMessage")
        return SQSHandlerResponse(true)
    }

    private fun handleFailedSettlement(settlementResponse: FailedSettlementClientResponseTO): BoletoSettlementResult {
        val errorCode = when (settlementResponse.errorCode) {
            SettlementErrorCode.AFTER_HOURS,
            SettlementErrorCode.OTHER,
            ->
                BoletoSettlementStatus.VOIDED

            SettlementErrorCode.BEFORE_HOURS -> BoletoSettlementStatus.UNAUTHORIZED
        }

        return BoletoSettlementResult(
            gateway = FinancialServiceGateway.FRIDAY,
            status = errorCode,
            bankTransactionId = settlementResponse.transactionId,
            externalNsu = 0,
            externalTerminal = "",
            errorCode = errorCode.name,
            errorDescription = settlementResponse.errorMessage,
            finalPartnerName = settlementResponse.financialServiceGateway,
        )
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        logger.error(append("message", m), "SettlementResponseHandler#handleError", e)

        return SQSHandlerResponse(false)
    }
}

enum class SettlementResponseStatus {
    NOT_FOUND, REFUNDED, SCHEDULED, PROCESSING, PAID, FAILED, LOCKED, BAD_REQUEST
}

enum class SettlementErrorCode {
    BEFORE_HOURS, AFTER_HOURS, OTHER
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "status",
)
sealed interface SettlementClientResponseTO {
    val transactionId: String
    val status: SettlementResponseStatus
    val timestamp: String
}

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonTypeName("PAID")
data class PaidSettlementClientResponseTO(
    override val transactionId: String,
    val barcode: String,
    val digitableLine: String,
    val authorization: String,
    override val timestamp: String,
    val financialServiceGateway: FinancialServiceGateway,
    val settlementFinancialInstitution: String,
) : SettlementClientResponseTO {
    override val status: SettlementResponseStatus = SettlementResponseStatus.PAID
}

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonTypeName("FAILED")
data class FailedSettlementClientResponseTO(
    override val transactionId: String,
    val barcode: String,
    val digitableLine: String,
    val errorMessage: String,
    override val timestamp: String,
    val financialServiceGateway: FinancialServiceGateway,
    val errorCode: SettlementErrorCode,
) : SettlementClientResponseTO {
    override val status: SettlementResponseStatus = SettlementResponseStatus.FAILED
}

@JsonInclude(JsonInclude.Include.ALWAYS)
@JsonTypeName("BAD_REQUEST")
data class BadRequestSettlementClientResponseTO(
    override val transactionId: String,
    override val timestamp: String,
    val errorMessage: String,
) : SettlementClientResponseTO {
    override val status: SettlementResponseStatus = SettlementResponseStatus.BAD_REQUEST
}