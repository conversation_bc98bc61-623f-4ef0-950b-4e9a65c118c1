package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.wallet.WalletId
import java.time.Duration

class PaymentMethodNotFound(override var message: String) : Exception() {

    constructor(accountId: AccountId) : this("No valid payment method for accountId: $accountId")

    constructor(walletId: WalletId) : this("No valid payment method for walletId: $walletId")

    constructor(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
    ) : this("PaymentMethodId ${accountPaymentMethodId.value} not found on AccountID ${accountId.value}")

    constructor(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ) : this("PaymentMethod not found on BankNo: $bankNo RoutingNo: $routingNo and AccountNo: ${accountNo.fullAccountNumber}")
}

class AccountNotFoundException(userId: String?) : RuntimeException(String.format("Account %s not found", userId))

class MobilePhoneAlreadyInUseException(val accountId: AccountId) : Exception()

class TokenStillValidException(val accountId: AccountId, val duration: Duration, val cooldown: Duration) : Exception()

class IssueTokenMaxLimitExceeded : Exception()

class InvalidTokenException(val reason: InvalidTokenReason) : Exception()

enum class InvalidTokenReason {
    NOT_FOUND,
    EXPIRED,
    MAX_ATTEMPTS,
    MISMATCH,
}

class AccountIsNotUnderReview(val currentStatus: AccountStatus) : Exception()
class DocumentscopyNotReady() : Exception()
class DocumentscopyGenericException : Exception {
    constructor(e: Exception) : super(e)
    constructor(msg: String) : super(msg)
}
class AccountIsNotUnderExternalReview(val currentStatus: AccountStatus) : Exception()
class AccountIsBlockedForEdition() : Exception()
class UpgradeIsNotUnderReview(val currentStatus: UpgradeStatus?) : Exception()
class InvalidMonthlyIncomeException : Exception()

class RegisterIncompleteException : Exception {
    constructor() : super("A field is missing", null, true, false)
    constructor(missingField: String) : super("Field $missingField is missing", null, true, false)
}

class RegisterNaturalPersonException : Exception()

class UserJourneyRegisterException(throwable: Throwable) : Exception(throwable)
class UserJourneyTrackEventException(throwable: Throwable) : Exception(throwable)