package ai.friday.billpayment.app.banking

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.integrations.EndToEnd
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.LocalDate
import java.time.ZonedDateTime

@JsonIgnoreProperties(ignoreUnknown = true)
class BankStatement(val items: List<BankStatementItem>, val initialBalance: Balance = Balance(0), val finalBalance: Balance = Balance(0)) {
    val checkedBalance = initialBalance + Balance(items.checkBalance())
}

class OmnibusBankStatement(val items: List<OmnibusBankStatementItem>) {
    val balance = Balance(
        items.map {
            it.bankStatementItem
        }.checkBalance(),
    )

    val unresolvedDeposits = items.filter {
        it.bankStatementItem.flow == BankStatementItemFlow.CREDIT && it.virtualPaymentMethodId == null && it.bankStatementItem.ref == null
    }.map {
        it.bankStatementItem
    }
}

private fun List<BankStatementItem>.checkBalance(): Long {
    return fold(0L) { acc, curr ->
        when (curr.flow) {
            BankStatementItemFlow.CREDIT -> acc + curr.amount
            BankStatementItemFlow.DEBIT -> acc - curr.amount
        }
    }
}

interface BankStatementItem {
    val date: LocalDate
    val flow: BankStatementItemFlow
    val type: BankStatementItemType
    val description: String
    val operationNumber: String
    val endToEnd: EndToEnd?
    val amount: Long
    val counterpartName: String
    val counterpartDocument: String
    val counterpartAccountNo: String?
    val counterpartBankName: String?
    val documentNumber: String
    val ref: String?
    val lastUpdate: ZonedDateTime?
    val metadata: BankStatementMetadata?
    val notificatedAt: ZonedDateTime?

    val isTemporaryOperationNumber: Boolean
        get() = operationNumber.length <= 7
}

data class DefaultBankStatementItem(
    override val date: LocalDate,
    override val flow: BankStatementItemFlow,
    override val type: BankStatementItemType,
    override val description: String,
    override val operationNumber: String,
    override val amount: Long,
    override val counterpartName: String,
    override val counterpartDocument: String,
    override val counterpartAccountNo: String? = null,
    override val counterpartBankName: String? = null,
    override val documentNumber: String,
    override val ref: String? = null,
    override val lastUpdate: ZonedDateTime? = null,
    override val metadata: BankStatementMetadata? = null,
    override val notificatedAt: ZonedDateTime? = null,
) : BankStatementItem {
    override val endToEnd: EndToEnd? = null
}

data class BankStatementMetadata(
    val virtualAmount: Long?,
)

data class OmnibusBankStatementItem(
    val bankStatementItem: BankStatementItem,
    val omnibusPaymentMethodId: AccountPaymentMethodId,
    val virtualPaymentMethodId: AccountPaymentMethodId?,
)

data class InternalBankStatementItem(
    val bankStatementItem: BankStatementItem,
    val accountPaymentMethodId: AccountPaymentMethodId,
)

enum class BankStatementItemFlow {
    CREDIT, DEBIT
}

enum class BankStatementItemType {
    TED_MESMA_TITULARIDADE, TED_DIF_TITULARIDADE, DEVOLUCAO_TED, TRANSFERENCIA_CC, PIX, DEVOLUCAO_PIX, INVESTMENT_REDEMPTION, OUTROS, PAGAMENTO_BOLETO
}