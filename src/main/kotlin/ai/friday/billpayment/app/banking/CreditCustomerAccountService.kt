package ai.friday.billpayment.app.banking

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.integrations.CustomerAccountCredit
import ai.friday.billpayment.app.integrations.CustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCreditReason
import ai.friday.billpayment.app.integrations.CustomerAccountCreditRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.ZonedDateTime

@FridayMePoupe
class CreditCustomerAccountService(
    private val customerAccountCreditRepository: CustomerAccountCreditRepository,
) {
    fun register(
        accountPaymentMethodId: AccountPaymentMethodId,
        amount: Long,
        fromInternalBankAccount: InternalBankAccountType,
        details: CustomerAccountCreditDetails,
        createdAt: ZonedDateTime = getZonedDateTime(), // FIXME - remover depois de rodar script
    ): CustomerAccountCredit {
        return CustomerAccountCredit(
            accountPaymentMethodId = accountPaymentMethodId,
            amount = amount,
            fromInternalBankAccount = fromInternalBankAccount,
            details = details,
            createdAt = createdAt,
        ).also {
            customerAccountCreditRepository.save(it)
        }
    }

    fun findAllByCreditDate(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime, // inclusive
    ): List<CustomerAccountCredit> = customerAccountCreditRepository.findByCreditDate(
        accountPaymentMethodId = accountPaymentMethodId,
        startDate = startDate,
        endDate = endDate,
    )

    fun findRefundedBillByPaymentDate(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime, // inclusive
    ): List<CustomerAccountCredit> = customerAccountCreditRepository.findByReasonAndIndexedDate(
        accountPaymentMethodId = accountPaymentMethodId,
        reason = CustomerAccountCreditReason.REFUNDED_BILL,
        startDate = startDate,
        endDate = endDate,
    )
}