package ai.friday.billpayment.app.banking

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.lock.internalBankServiceLockProvider
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.CustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.FundProvider
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.isBetween
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.pix.PixStatementItem
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Named("InternalBankService")
@FridayMePoupe
open class InternalBankService(
    private val accountService: AccountService,
    private val bankAccountService: BankAccountService,
    private val pixPaymentService: PixPaymentService,
    private val internalBankRepository: InternalBankRepository,
    private val messagePublisher: MessagePublisher,
    private val omnibusBankAccountConfiguration: OmnibusBankAccountConfiguration,
    private val balanceService: BalanceService,
    private val notificationAdapter: NotificationAdapter,
    private val walletService: WalletService,
    private val creditCustomerAccountService: CreditCustomerAccountService,
    @Named(internalBankServiceLockProvider) private val lockProvider: InternalLock,
) : FundProvider {

    @field:Property(name = "integrations.arbi.contaLiquidacao")
    lateinit var internalSettlementAccountNo: String

    @field:Property(name = "integrations.arbi.contaCashin")
    lateinit var internalCashinAccountNo: String

    @field:Property(name = "sqs.bankAccountDepositQueueName")
    lateinit var bankAccountDepositQueueName: String

    @field:Property(name = "internalBankService.checkStatus.startDateMinusDays")
    var startDateMinusDays: Long = 0

    @field:Property(name = "internalBankService.checkStatus.endDatePlusDays")
    var endDatePlusDays: Long = 0

    private val mapper = getObjectMapper()

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun synchronizeBankAccount(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Boolean {
        val paymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
            accountPaymentMethodId = accountPaymentMethodId,
            accountId = accountId,
        )
        val bankAccount = paymentMethod.method as InternalBankAccount
        return synchronizeBankAccount(
            accountId = paymentMethod.accountId,
            accountPaymentMethodId = paymentMethod.id,
            accountNumber = bankAccount.accountNumber,
            document = bankAccount.document,
        )
    }

    fun synchronizeBankAccount(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
    ): Boolean {
        if (omnibusBankAccountConfiguration.isOmnibusBankAccount(bankNo, routingNo, accountNumber)) {
            return synchronizeOmnibusBankAccount()
        }

        val paymentMethod = getPaymentMethod(bankNo, routingNo, accountNumber)
        val bankAccount = paymentMethod.method as InternalBankAccount

        return synchronizeBankAccount(
            accountId = paymentMethod.accountId,
            accountPaymentMethodId = paymentMethod.id,
            accountNumber = bankAccount.accountNumber,
            document = bankAccount.document,
        )
    }

    fun synchronizeBankAccount(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        accountNumber: AccountNumber,
        document: String,
    ): Boolean {
        val logName = "InternalBankService#synchronizeBankAccount"
        val markers = append("accountId", accountId.value)
            .andAppend("accountPaymentMethodId", accountPaymentMethodId.value)
            .andAppend("sourceFullAccountNo", accountNumber)
            .andAppend("document", document)
        try {
            val pixStatementItems = pixPaymentService.getStatement(accountNumber)
            markers.andAppend("pixStatementItems", pixStatementItems)

            val pixSynchronizedItems = pixStatementItems.synchronize(accountPaymentMethodId, accountId, document)
            markers.andAppend("pixSynchronizedItems", pixSynchronizedItems)

            val defaultBankStatementItems = getBankStatmentItems(
                accountNumber = accountNumber,
                document = document,
            ).filter { it.type !in listOf(BankStatementItemType.PIX, BankStatementItemType.DEVOLUCAO_PIX) }
            markers.andAppend("defaultBankStatementItems", defaultBankStatementItems)

            val defaultSynchronizedItems = defaultBankStatementItems.synchronize(accountPaymentMethodId, accountId, document)
            markers.andAppend("defaultSynchronizedItems", defaultSynchronizedItems)

            checkInternalStatementIntegrity(
                accountPaymentMethodId = accountPaymentMethodId,
                externalBankStatementItems = defaultBankStatementItems + pixStatementItems,
            )

            logger.info(markers, logName)
            return pixSynchronizedItems.isNotEmpty() || defaultSynchronizedItems.isNotEmpty()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw SynchronizeBankAccountException(e)
        }
    }

    open fun notifyPixNotReceived(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
        flow: BankStatementItemFlow,
        counterpartName: String,
        counterpartDocument: String,
        amount: Long,
    ) {
        val paymentMethod =
            if (omnibusBankAccountConfiguration.isOmnibusBankAccount(bankNo, routingNo, accountNumber)) {
                val virtualAccount = findVirtualAccountPaymentMethodOrNull(flow, counterpartDocument)
                if (virtualAccount == null) {
                    LOG.error(append("context", "virtual account not found"), "notifyPixNotReceived")
                    return
                }
                virtualAccount
            } else {
                getPaymentMethod(bankNo, routingNo, accountNumber)
            }

        val wallet = walletService.findWallet(
            paymentMethod.accountId,
            paymentMethod.id,
        )

        notificationAdapter.notifyPixNotReceivedFailure(
            accountId = paymentMethod.accountId,
            wallet = wallet,
            amount = amount,
            senderName = counterpartName,
            senderDocument = counterpartDocument,
        )
    }

    open fun synchronizePixBankStatementItem(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
        bankStatementItem: BankStatementItem,
    ) {
        if (omnibusBankAccountConfiguration.isOmnibusBankAccount(bankNo, routingNo, accountNumber)) {
            synchronizeOmnibusBankAccount()
            return
        }

        val paymentMethod = getPaymentMethod(bankNo, routingNo, accountNumber)

        if (shouldSynchronize(paymentMethod.id, bankStatementItem)) {
            // estamos agora sincronizando itens com operation number temporario. usamos o endToEnd para
            // sincronizar e fazer o match quando o operation number virar definitivo no extrato externo
            synchronize(
                accountPaymentMethodId = paymentMethod.id,
                accountId = paymentMethod.accountId,
                item = bankStatementItem,
            )
        }
    }

    fun resolveTemporaryOperationNumber(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
        temporaryOperationNumber: String,
    ): Boolean {
        val logName = "InternalBankService#resolveTemporaryOperationNumber"
        val markers = Markers.append("accountNumber", accountNumber)
            .andAppend("routingNo", routingNo)
            .andAppend("temporaryOperationNumber", temporaryOperationNumber)

        val paymentMethod = getPaymentMethod(bankNo, routingNo, accountNumber)
        markers.andAppend("accoundId", paymentMethod.accountId.value)
            .andAppend("accountPaymentMethodId", paymentMethod.id.value)

        val temporaryItem = internalBankRepository.findBankStatementItem(paymentMethod.id, temporaryOperationNumber).getOrElse {
            markers.andAppend("error", it)
            logger.warn(markers, logName)
            return when (it) {
                FindError.MultipleItemsFound -> false
                FindError.NotFound -> true
                is FindError.ServerError -> false
            }
        }
        markers.andAppend("temporaryItem", temporaryItem)
        markers.andAppend("endToEnd", temporaryItem.endToEnd!!.value)

        val resolvedWithPixStatement = resolveWithPixStatement(accountNumber, temporaryItem, markers, paymentMethod)
        markers.andAppend("resolvedWithPixStatement", resolvedWithPixStatement)
        if (resolvedWithPixStatement) {
            logger.info(markers, logName)
            return true
        }

        val resolvedWithDefaultStatement = resolveWithDefaultStatement(paymentMethod, accountNumber, markers, temporaryItem)
        markers.andAppend("resolvedWithDefaultStatement", resolvedWithDefaultStatement)
        logger.info(markers, logName)
        return resolvedWithDefaultStatement
    }

    private fun resolveWithDefaultStatement(
        paymentMethod: AccountPaymentMethod,
        accountNumber: AccountNumber,
        markers: LogstashMarker,
        temporaryItem: BankStatementItem,
    ): Boolean {
        val bankAccount = paymentMethod.method as InternalBankAccount
        val defaultBankStatementItems = getBankStatmentItems(
            accountNumber = accountNumber,
            document = bankAccount.document,
        ).filter { it.type in listOf(BankStatementItemType.PIX, BankStatementItemType.DEVOLUCAO_PIX) }
        markers.andAppend("defaultBankStatementItems", defaultBankStatementItems)

        val defaultItems = defaultBankStatementItems.filter {
            !it.isTemporaryOperationNumber &&
                it.amount == temporaryItem.amount &&
                it.counterpartDocument == temporaryItem.counterpartDocument
            it.date == temporaryItem.date
        }
        markers.andAppend("defaultItems", defaultItems)
        defaultItems.forEach { defaultDefinitiveItem ->
            val internalItem = internalBankRepository.findBankStatementItem(paymentMethod.id, defaultDefinitiveItem.operationNumber).getOrNull()
            if (internalItem == null) {
                markers.andAppend("newOperationNumber", defaultDefinitiveItem.operationNumber)
                internalBankRepository.create(InternalBankStatementItem((temporaryItem as PixStatementItem).copy(operationNumber = defaultDefinitiveItem.operationNumber, notificatedAt = temporaryItem.notificatedAt), paymentMethod.id))
                internalBankRepository.remove(InternalBankStatementItem(temporaryItem, paymentMethod.id))
                return true
            }
        }
        return false
    }

    private fun resolveWithPixStatement(
        accountNumber: AccountNumber,
        temporaryItem: BankStatementItem,
        markers: LogstashMarker,
        paymentMethod: AccountPaymentMethod,
    ): Boolean {
        val pixStatement = pixPaymentService.getStatement(accountNumber)
        markers.andAppend("pixStatement", pixStatement)

        val pixDefinitiveItem = pixStatement.singleOrNull { it.endToEnd == temporaryItem.endToEnd && !it.isTemporaryOperationNumber }
        markers.andAppend("pixDefinitiveItem", pixDefinitiveItem)
        if (pixDefinitiveItem != null) {
            markers.andAppend("newOperationNumber", pixDefinitiveItem.operationNumber)
            internalBankRepository.create(InternalBankStatementItem(pixDefinitiveItem.copy(notificatedAt = temporaryItem.notificatedAt), paymentMethod.id))
            internalBankRepository.remove(InternalBankStatementItem(temporaryItem, paymentMethod.id))
            return true
        }
        return false
    }

    fun synchronizeOmnibusBankAccount(): Boolean {
        val accountPaymentMethodId = AccountPaymentMethodId(omnibusBankAccountConfiguration.accountPaymentMethodId)
        val sourceFullAccountNo = omnibusBankAccountConfiguration.accountNo
        val document = omnibusBankAccountConfiguration.document
        try {
            val now = getZonedDateTime()
            val startDate = now.minusDays(startDateMinusDays)
            val endDate = now.plusDays(endDatePlusDays)
            val bankStatement = bankAccountService.getStatement(
                accountNumber = AccountNumber(sourceFullAccountNo),
                document = document,
                initialDate = startDate,
                endDate = endDate,
            )
            var synchronizedAnyStatements = false
            bankStatement.items.forEach { item ->
                if (synchronizeOmnibusBankStatementItem(item, accountPaymentMethodId)) {
                    synchronizedAnyStatements = true
                }
            }

            return synchronizedAnyStatements
        } catch (e: Exception) {
            throw SynchronizeBankAccountException(e)
        }
    }

    private fun getBankStatmentItems(
        accountNumber: AccountNumber,
        document: String,
    ): List<BankStatementItem> {
        val now = getZonedDateTime()
        val startDate = now.minusDays(startDateMinusDays)
        val endDate = now.plusDays(endDatePlusDays)
        val bankStatement = bankAccountService.getStatement(
            accountNumber = accountNumber,
            document = document,
            initialDate = startDate,
            endDate = endDate,
        )
        return bankStatement.items
    }

    private fun List<BankStatementItem>.synchronize(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        document: String,
    ): List<BankStatementItem> = mapNotNull { item ->
        trySynchronizeItem(accountPaymentMethodId, item, accountId, document)
    }

    private fun trySynchronizeItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        item: BankStatementItem,
        accountId: AccountId,
        document: String,
    ): BankStatementItem? {
        val markers = append("accountId", accountId.value)
            .andAppend("document", document)
            .andAppend("operationNumber", item.operationNumber)
            .andAppend("amount", item.amount)
            .andAppend("itemDate", item.date)

        val shouldSynchronize = shouldSynchronize(accountPaymentMethodId, item)
        markers.andAppend("shouldSynchronize", shouldSynchronize)

        return if (shouldSynchronize) {
            synchronize(
                accountPaymentMethodId = accountPaymentMethodId,
                accountId = accountId,
                item = item,
            )
        } else {
            null
        }.also {
            markers.andAppend("syncResult", it != null)
            logger.info(markers, "InternalBankService#synchronizeBankAccountItem")
        }
    }

    fun isAfterHour(time: ZonedDateTime): Boolean {
        val afterHoursTimeLimit = LocalTime.of(17, 0)
        val afterHoursLimit = ZonedDateTime.of(time.toLocalDate(), afterHoursTimeLimit, brazilTimeZone)

        val isHoliday = FinancialInstitutionGlobalData.isHoliday(time.toLocalDate())
        val isWeekend = time.dayOfWeek in listOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
        return time.isAfter(afterHoursLimit) or isWeekend or isHoliday
    }

    override fun captureFunds(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        paymentMethod: PaymentMethod,
        amount: Long,
    ): BankTransfer {
        val costumerBankAccount = paymentMethod as InternalBankAccount
        return when (costumerBankAccount.bankAccountMode) {
            BankAccountMode.PHYSICAL -> captureFundsPhysicalAccount(costumerBankAccount, amount)
            BankAccountMode.VIRTUAL -> captureFundsVirtualAccount(accountPaymentMethodId, costumerBankAccount, amount)
        }
    }

    private fun InternalBankAccountType.fullAccountNumber() = when (this) {
        InternalBankAccountType.SETTLEMENT -> internalSettlementAccountNo
    }

    fun debitCustomer(
        customerAccountNumber: AccountNumber,
        amount: Long,
        toInternalBankAccount: InternalBankAccountType,
    ): BankTransfer {
        return bankAccountService.transfer(
            originAccountNo = customerAccountNumber.fullAccountNumber,
            targetAccountNo = toInternalBankAccount.fullAccountNumber(),
            amount = amount,
        )
    }

    fun creditCustomer(
        customerPaymentMethod: AccountPaymentMethod,
        amount: Long,
        fromInternalBankAccount: InternalBankAccountType,
        details: CustomerAccountCreditDetails,
        operationId: BankOperationId = BankOperationId.build(),
    ): BankTransfer {
        val logName = "InternalBankService#creditCustomer"
        val markers = append("params.customerPaymentMethod", customerPaymentMethod)
            .andAppend("params.amount", amount)
            .andAppend("params.fromInternalBankAccount", fromInternalBankAccount)
            .andAppend("params.details", details)
            .andAppend("params.operationId", operationId.value)

        val customerAccountNumber = (customerPaymentMethod.method as InternalBankAccount).accountNumber

        return bankAccountService.transfer(
            originAccountNo = fromInternalBankAccount.fullAccountNumber(),
            targetAccountNo = customerAccountNumber.fullAccountNumber,
            amount = amount,
            operationId = operationId,
        ).also {
            logger.info(markers.andAppend("bankTransfer", it), logName)
            if (it.status == BankOperationStatus.SUCCESS) {
                creditCustomerAccountService.register(
                    accountPaymentMethodId = customerPaymentMethod.id,
                    amount = amount,
                    fromInternalBankAccount = fromInternalBankAccount,
                    details = details,
                )
            }
        }
    }

    override fun undoCaptureFunds(
        accountPaymentMethod: AccountPaymentMethod,
        operationId: BankOperationId,
        amount: Long,
        details: CustomerAccountCreditDetails,
        ongoingRefundOperationId: BankOperationId?,
    ): BankTransfer {
        val costumerBankAccount = accountPaymentMethod.method as InternalBankAccount

        val bankTransferResult = creditCustomer(
            customerPaymentMethod = accountPaymentMethod,
            amount = amount,
            fromInternalBankAccount = InternalBankAccountType.SETTLEMENT,
            details = details,
        )
        if (costumerBankAccount.bankAccountMode == BankAccountMode.VIRTUAL && bankTransferResult.status == BankOperationStatus.SUCCESS) {
            val internalBankStatementItem = InternalBankStatementItem(
                accountPaymentMethodId = accountPaymentMethod.id,
                bankStatementItem = buildBankStatementItem(
                    BankStatementItemFlow.CREDIT,
                    bankTransferResult,
                    operationId,
                ),
            )
            try {
                internalBankRepository.create(internalBankStatementItem)
            } catch (error: IllegalStateException) {
                LOG.error(
                    append("error", error.message)
                        .andAppend("internalBankStatementItem", internalBankStatementItem),
                    "UndoCaptureFunds",
                )
            }
        }

        return bankTransferResult
    }

    fun prepareCashInFunds(amount: Long) = BankTransfer(
        operationId = BankOperationId.build(),
        gateway = FinancialServiceGateway.ARBI,
        status = BankOperationStatus.UNKNOWN,
        amount = amount,
    )

    fun cashInFunds(operationId: BankOperationId, userFullAccountNo: String, amount: Long): BankTransfer {
        return bankAccountService.transfer(
            originAccountNo = internalCashinAccountNo,
            targetAccountNo = userFullAccountNo,
            amount = amount,
            operationId = operationId,
        )
    }

    override fun checkCaptureFunds(
        accountId: AccountId,
        paymentMethod: PaymentMethod,
        operationId: BankOperationId,
        operationDate: LocalDate,
    ): Boolean {
        val costumerBankAccount = paymentMethod as InternalBankAccount
        return bankAccountService.checkTransferStatus(
            originAccountNo = costumerBankAccount.buildFullAccountNumber(),
            idRequisicaoParceiroOriginal = operationId.value,
            startDate = operationDate.minusDays(startDateMinusDays),
            endDate = operationDate.plusDays(endDatePlusDays),
        )
    }

    fun checkCashInFunds(operationId: BankOperationId, operationDate: LocalDate): Boolean {
        return bankAccountService.checkTransferStatus(
            originAccountNo = internalCashinAccountNo,
            idRequisicaoParceiroOriginal = operationId.value,
            startDate = operationDate.minusDays(startDateMinusDays),
            endDate = operationDate.plusDays(endDatePlusDays),
        )
    }

    fun getCashInByPeriod(
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Long {
        val credits = internalBankRepository.findAllBankStatementCredits(
            accountPaymentMethodId = accountPaymentMethodId,
            startDate = startDate.toLocalDate(),
            endDate = endDate.toLocalDate(),
        )

        return credits.sumOf { it.amount }
    }

    open fun logStatementDivergence(accountPaymentMethodId: AccountPaymentMethodId, operationNumber: String, message: String) {
        LOG.error(
            append("accountPaymentMethodId", accountPaymentMethodId.value)
                .andAppend("operationNumber", operationNumber)
                .andAppend("error", message),
            "SynchronizeInternalStatement",
        )
    }

    open fun logEndToEndToBeResolved(accountPaymentMethodId: AccountPaymentMethodId, operationNumber: String, endToEnd: EndToEnd) {
        LOG.warn(
            append("accountPaymentMethodId", accountPaymentMethodId.value)
                .andAppend("operationNumber", operationNumber)
                .andAppend("isTemporaryOperationNumber", (operationNumber.length <= 7))
                .andAppend("endToEnd", endToEnd.value)
                .andAppend("error", "Statement interno com o mesmo endToEnd mas operationNumber diferente"),
            "SynchronizeInternalStatement",
        )
    }

    open fun logDuplicatedEndToEnd(accountPaymentMethodId: AccountPaymentMethodId, endToEnd: EndToEnd) {
        LOG.error(
            append("accountPaymentMethodId", accountPaymentMethodId.value)
                .andAppend("endToEnd", endToEnd.value)
                .andAppend("error", "Statement (endToEnd) duplicado no extrato interno"),
            "SynchronizeInternalStatement",
        )
    }

    private fun synchronize(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        item: BankStatementItem,
    ): BankStatementItem? {
        val internalBankStatement = InternalBankStatementItem(item, accountPaymentMethodId)
        try {
            internalBankRepository.create(internalBankStatement)
            balanceService.invalidate(accountPaymentMethodId)
            publishBankOperationExecuted(item, accountId, accountPaymentMethodId)
            return item
        } catch (error: IllegalStateException) {
            LOG.warn(
                append("error", error.message)
                    .andAppend("internalBankStatement", internalBankStatement),
                "SynchronizeInternalBankStatement",
            )
            return null
        }
    }

    private fun checkInternalStatementIntegrity(
        accountPaymentMethodId: AccountPaymentMethodId,
        externalBankStatementItems: List<BankStatementItem>,
    ) {
        val internalBankStatement = internalBankRepository.findAllBankStatementItem(
            accountPaymentMethodId = accountPaymentMethodId,
            startDate = getLocalDate().minusDays(startDateMinusDays),
            endDate = getLocalDate().plusDays(endDatePlusDays),
        )

        val internalOperationMap = internalBankStatement.items.associateBy { it.operationNumber }
        val externalOperationMap = externalBankStatementItems.associateBy { it.operationNumber }

        val unknownOperationNumbers = internalOperationMap.keys - externalOperationMap.keys
        unknownOperationNumbers.forEach { operationNumber ->
            handleUnknownOperationNumbers(accountPaymentMethodId, operationNumber, internalOperationMap, externalBankStatementItems)
        }

        val duplicatedItems = internalBankStatement.items.groupingBy { it.operationNumber }.eachCount()
            .filter { (_, count) -> count >= 2 }
        duplicatedItems.forEach { duplicatedItem ->
            logStatementDivergence(accountPaymentMethodId, duplicatedItem.key, "Statement duplicado no extrato interno.")
        }

        val duplicatedEndToEnd = internalBankStatement.items.filter { it.endToEnd != null }.groupingBy { it.endToEnd }.eachCount()
            .filter { (_, count) -> count >= 2 }
        duplicatedEndToEnd.forEach { duplicatedItem ->
            logDuplicatedEndToEnd(accountPaymentMethodId, duplicatedItem.key!!)
        }
    }

    private fun handleUnknownOperationNumbers(
        accountPaymentMethodId: AccountPaymentMethodId,
        operationNumber: String,
        internalOperationMap: Map<String, BankStatementItem>,
        externalBankStatementItems: List<BankStatementItem>,
    ) {
        val internalStatementItem = internalOperationMap.get(operationNumber)
        when (val intEndToEnd = internalStatementItem!!.endToEnd) {
            // se houver operationNumber desconhecido, verificamos se o endToEnd existe e se há match com algum item do extrato externo
            null -> logStatementDivergence(
                accountPaymentMethodId,
                operationNumber,
                "Statement interno não encontrado no extrato externo.",
            )
            // match de endToEnd, esse caso provavelmente está na fila de resolução do operationNumber temporario
            else ->
                externalBankStatementItems
                    .singleOrNull { it.endToEnd == intEndToEnd }
                    ?.let { matchingExternal ->
                        if (internalStatementItem.isTemporaryOperationNumber) {
                            logEndToEndToBeResolved(accountPaymentMethodId, operationNumber, intEndToEnd)
                        }
                    }
        }
    }

    private fun captureFundsPhysicalAccount(costumerBankAccount: InternalBankAccount, amount: Long): BankTransfer {
        return bankAccountService.transfer(
            originAccountNo = costumerBankAccount.buildFullAccountNumber(),
            targetAccountNo = internalSettlementAccountNo,
            amount = amount,
        )
    }

    private fun captureFundsVirtualAccount(
        accountPaymentMethodId: AccountPaymentMethodId,
        costumerBankAccount: InternalBankAccount,
        amount: Long,
    ): BankTransfer {
        val lock = lockProvider.acquireLock(accountPaymentMethodId.value)
            ?: return buildBankTransferConcurrentLockError(amount)
        try {
            val currentBalance = internalBankRepository.findAllBankStatementItem(accountPaymentMethodId).checkedBalance.amount
            if (currentBalance < amount) {
                return buildBankTransferInsufficientFunds(amount)
            }
            val bankTransferResult = bankAccountService.transfer(
                originAccountNo = costumerBankAccount.buildFullAccountNumber(),
                targetAccountNo = internalSettlementAccountNo,
                amount = amount,
            )
            if (bankTransferResult.status == BankOperationStatus.SUCCESS) {
                val internalBankStatementItem = InternalBankStatementItem(
                    accountPaymentMethodId = accountPaymentMethodId,
                    bankStatementItem = buildBankStatementItem(BankStatementItemFlow.DEBIT, bankTransferResult),
                )
                try {
                    internalBankRepository.create(internalBankStatementItem)
                } catch (error: IllegalStateException) {
                    LOG.error(
                        append("error", error.message)
                            .andAppend("internalBankStatementItem", internalBankStatementItem),
                        "CaptureFundsVirtualAccount",
                    )
                }
            }
            return bankTransferResult
        } finally {
            lock.unlock()
        }
    }

    private fun getPaymentMethod(
        bankNo: Long,
        routingNo: Long,
        accountNumber: AccountNumber,
    ) = accountService.findPhysicalAccountPaymentMethod(bankNo, routingNo, accountNumber.fullAccountNumber)
        .getOrElse {
            when (it) {
                FindError.MultipleItemsFound -> throw IllegalStateException("Payment method list size must be 1")
                FindError.NotFound -> throw PaymentMethodNotFound(bankNo, routingNo, accountNumber)
                is FindError.ServerError -> throw it.exception
            }
        }

    private fun buildBankStatementItem(
        flow: BankStatementItemFlow,
        bankTransfer: BankTransfer,
        operationId: BankOperationId? = null,
    ): DefaultBankStatementItem {
        return DefaultBankStatementItem(
            date = getZonedDateTime().toLocalDate(),
            flow = flow,
            type = BankStatementItemType.TRANSFERENCIA_CC,
            description = "",
            operationNumber = if (flow == BankStatementItemFlow.DEBIT) bankTransfer.debitOperationNumber!! else bankTransfer.creditOperationNumber!!,
            amount = bankTransfer.amount,
            counterpartName = omnibusBankAccountConfiguration.name,
            counterpartDocument = omnibusBankAccountConfiguration.document,
            documentNumber = "",
            ref = operationId?.value,
        )
    }

    private fun buildBankTransferInsufficientFunds(amount: Long): BankTransfer {
        return BankTransfer(
            operationId = BankOperationId.build(),
            status = BankOperationStatus.INSUFFICIENT_FUNDS,
            amount = amount,
            errorDescription = "Insufficient funds on virtual account",
            gateway = FinancialServiceGateway.ARBI,
        ).also {
            LOG.warn(append("bankTransfer", it), "InternalBankService")
        }
    }

    private fun buildBankTransferConcurrentLockError(amount: Long): BankTransfer {
        return BankTransfer(
            operationId = BankOperationId.build(),
            status = BankOperationStatus.ERROR,
            amount = amount,
            errorDescription = "Concurrent transaction exception on virtual account",
            gateway = FinancialServiceGateway.ARBI,
        ).also {
            LOG.warn(append("bankTransfer", it), "InternalBankService")
        }
    }

    private fun synchronizeOmnibusBankStatementItem(
        item: BankStatementItem,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): Boolean {
        val targetVirtualAccount =
            findVirtualAccountPaymentMethodOrNull(flow = item.flow, counterpartDocument = item.counterpartDocument)

        if (shouldSynchronize(accountPaymentMethodId, item)) {
            internalBankRepository.save(
                OmnibusBankStatementItem(
                    item,
                    accountPaymentMethodId,
                    targetVirtualAccount?.id,
                ),
            )
            balanceService.invalidate(accountPaymentMethodId)
        }

        targetVirtualAccount?.let {
            if (shouldSynchronize(it.id, item)) {
                val internalBankStatementItem = InternalBankStatementItem(item, it.id)
                try {
                    internalBankRepository.create(internalBankStatementItem)
                    publishBankOperationExecuted(item, it.accountId, it.id)
                    return true
                } catch (error: IllegalStateException) {
                    LOG.error(
                        append("error", error.message)
                            .andAppend("internalBankStatementItem", internalBankStatementItem),
                        "SynchronizeOmnibusBankStatement",
                    )
                }
            }
        }

        return false
    }

    private fun findVirtualAccountPaymentMethodOrNull(
        flow: BankStatementItemFlow,
        counterpartDocument: String,
    ): AccountPaymentMethod? {
        return takeIf {
            flow == BankStatementItemFlow.CREDIT
        }?.let {
            accountService.findVirtualBankAccountByDocument(counterpartDocument).getOrElse { error ->
                when (error) {
                    FindError.NotFound -> null
                    FindError.MultipleItemsFound -> throw SynchronizeBankAccountException("document $counterpartDocument has multiple AccountPaymentMethods")
                    is FindError.ServerError -> throw SynchronizeBankAccountException(
                        "error searching account for document $counterpartDocument",
                        error.exception,
                    )
                }
            }
        }
    }

    private fun shouldSynchronize(accountPaymentMethodId: AccountPaymentMethodId, item: BankStatementItem): Boolean {
        // vamos sincronizar item com numero temporario e depois resolver com endToEnd
        if (item.isTemporaryOperationNumber && item.endToEnd == null) {
            return false
        }

        // se item for pix, procuramos na base pelo endToEnd. se encontrar, nao precisamos salvar
        val result = if (item is PixStatementItem && item.endToEnd != null) {
            internalBankRepository.findBankStatementItemByEndToEnd(accountPaymentMethodId, item.endToEnd!!)
        } else {
            internalBankRepository.findBankStatementItem(accountPaymentMethodId, item.operationNumber)
        }

        val internalBankStatementItem = result.getOrElse {
            when (it) {
                FindError.NotFound -> return true
                else -> throw SynchronizeBankAccountException("error finding bank statement item with account $accountPaymentMethodId and item $item")
            }
        }

        // query na base nao pode incluir a data -> arbi as vezes retorna itens com data diferente do que temos na base
        // tentavamos sincronizar equivocadamente esses itens com data diferente.
        val minDate = item.date.minusDays(startDateMinusDays)
        val maxDate = item.date.plusDays(endDatePlusDays)
        if (internalBankStatementItem.date.isBetween(minDate, maxDate)) {
            return false
        }
        return true
    }

    private fun publishBankOperationExecuted(
        item: BankStatementItem,
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ) {
        val operation = with(item) {
            BankOperationExecuted(
                accountId = accountId,
                accountPaymentMethodId = accountPaymentMethodId,
                created = Instant.now().toEpochMilli(),
                date = date.format(dateFormat),
                flow = flow,
                type = type,
                description = description,
                operationNumber = operationNumber,
                endToEnd = endToEnd,
                amount = amount,
                counterpartName = counterpartName,
                counterpartDocument = counterpartDocument,
                counterpartAccountNo = counterpartAccountNo,
                counterpartBankName = counterpartBankName,
                documentNumber = documentNumber,
            )
        }
        messagePublisher.sendMessage(QueueMessage(bankAccountDepositQueueName, mapper.writeValueAsString(operation)))
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(InternalBankService::class.java)
    }
}

data class BankOperationExecuted(
    val accountId: AccountId,
    val accountPaymentMethodId: AccountPaymentMethodId,
    val date: String,
    val created: Long,
    val flow: BankStatementItemFlow,
    val type: BankStatementItemType,
    val description: String,
    val operationNumber: String,
    val endToEnd: EndToEnd? = null,
    val amount: Long,
    val counterpartName: String,
    val counterpartDocument: String,
    val counterpartAccountNo: String? = null,
    val counterpartBankName: String? = null,
    val documentNumber: String,
) {
    fun verifyIsDeposit(): Boolean {
        return flow == BankStatementItemFlow.CREDIT && type in listOf(
            BankStatementItemType.TED_MESMA_TITULARIDADE,
            BankStatementItemType.TED_DIF_TITULARIDADE,
            BankStatementItemType.PIX,
        )
    }

    fun verifyIsRefund(): Boolean {
        return flow == BankStatementItemFlow.CREDIT && type == BankStatementItemType.DEVOLUCAO_TED
    }
}

sealed class FridayBankAccountError : PrintableSealedClassV2() {
    data object FridayAccountNotFound : FridayBankAccountError()
    data object FridayAccountClosed : FridayBankAccountError()
    class ServerError(val exception: Exception) : FridayBankAccountError()
}

data class SynchronizeBankAccountTO(
    val bankNo: Long,
    val routingNo: Long,
    val fullAccountNumber: String,
)

data class ResolveStatementItemTemporaryNumberTO(
    val bankNo: Long,
    val routingNo: Long,
    val fullAccountNumber: String,
    val temporaryOperationNumber: String,
)

enum class InternalBankAccountType {
    SETTLEMENT,
}