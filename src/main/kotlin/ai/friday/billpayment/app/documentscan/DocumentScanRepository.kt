package ai.friday.billpayment.app.documentscan

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.liveness.LivenessProvider
import arrow.core.Either
import jakarta.inject.Named
import jakarta.inject.Singleton

@Singleton
class DocumentScanRepositoryChooser(
    @Named("FACETEC") private val livenessGateway: DocumentScanRepository,
    @Named("CAF")private val cafGateway: DocumentScanRepository,
) {
    fun choose(documentScan: DocumentScan): DocumentScanRepository = when (documentScan.documentScanId.provider) {
        LivenessProvider.FACETEC -> livenessGateway
        LivenessProvider.CAF -> cafGateway
    }

    fun choose(payload: DocumentScanPayloadData): DocumentScanRepository = when (payload) {
        is FacetecDocumentScanPayloadData -> livenessGateway
        is CAFDocumentScanPayloadData -> cafGateway
    }
}

interface DocumentScanRepository {
    fun upsertDocumentScanId(payload: DocumentScanPayloadData): Either<UpsertDocumentScanIdError, DocumentScan>

    fun getResult(id: DocumentScanId): Either<GetDocumentScanResultError, DocumentScanResult>

    fun getImage(id: DocumentScanId): Either<GetImageError, DocumentScanImage>
}

sealed class DocumentScanPayloadData(
    open val accountId: AccountId,
)

data class FacetecDocumentScanPayloadData(
    override val accountId: AccountId,
    val documentType: DocumentType,
) : DocumentScanPayloadData(accountId = accountId)

data class CAFDocumentScanPayloadData(
    override val accountId: AccountId,
    val jwt: String,
    val referenceToken: String,
) : DocumentScanPayloadData(accountId = accountId)