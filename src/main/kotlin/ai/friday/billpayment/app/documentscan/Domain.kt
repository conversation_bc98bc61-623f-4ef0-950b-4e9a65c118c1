package ai.friday.billpayment.app.documentscan

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.liveness.LivenessProvider

data class DocumentScanId(val value: String, val provider: LivenessProvider, val referenceToken: String?, val documentscopyTransactionId: String?)

sealed class UpsertDocumentScanIdError {
    data class Unexpected(val e: Throwable) : UpsertDocumentScanIdError()
    data object AccountNotFound : UpsertDocumentScanIdError()
    data object IllegalAccountStatus : UpsertDocumentScanIdError()
    data object IllegalAccountType : UpsertDocumentScanIdError()
    data object IllegalAccountRole : UpsertDocumentScanIdError()
}

data class DocumentScanResult(
    val digitalSpoof: DocumentScanDigitalSpoofResult,
    val face: DocumentScanFaceResult,
    val text: DocumentScanTextResult,
    val ocr: DocumentScanOcrResult,
)

sealed class GetDocumentScanResultError(message: String?, cause: Throwable? = null) : Exception(message, cause) {
    data class Incomplete(val documentScanId: DocumentScanId) : GetDocumentScanResultError("Document scan is incomplete")
    data class AccountNotFound(val accountId: AccountId) : GetDocumentScanResultError("Document scan not found for provided account id")
    data class Unexpected(val exception: Throwable) : GetDocumentScanResultError("Unexpected error getting document scan image", exception)
}

data class DocumentScanDigitalSpoofResult(val status: DocumentScanDigitalSpoofStatus)

enum class DocumentScanDigitalSpoofStatus {
    /**
     * O documento é físico e não necessariamente está sem adulterações.
     */
    LIKELY_PHYSICAL_ID,

    /**
     * Análise para determinar se é um documento físico foi inconclusiva.
     */
    CANNOT_CONFIRM_PHYSICAL_ID,
}

sealed class DocumentScanFaceResult {
    data class Found(val status: DocumentScanFaceStatus) : DocumentScanFaceResult()
    data object NotFound : DocumentScanFaceResult()
}

enum class DocumentScanFaceStatus {
    /**
     * O rosto encontrado no documento provavelmente não sofreu adulteração.
     */
    LIKELY_ORIGINAL_FACE,

    /**
     * Análise de adulteração da foto foi inconclusiva.
     */
    CANNOT_CONFIRM_ID_IS_AUTHENTIC,

    /**
     * Foi detectado um template que permite que a foto seja colada (e.g. documento antigo de identidade).
     * Portanto, não é possível concluir se houve adulteração.
     */
    TEMPLATE_DOES_NOT_SUPPORT_DETECTION,
}

sealed class DocumentScanTextResult {
    data object NotPerformed : DocumentScanTextResult()
    data class Performed(val status: DocumentScanTextStatus) : DocumentScanTextResult()
}

enum class DocumentScanTextStatus {
    /**
     * O texto do documento provavelmente não sofreu adulterações.
     */
    LIKELY_ORIGINAL_TEXT,

    /**
     * Análise de adulteração do texto foi inconclusiva.
     */
    CANNOT_CONFIRM_ID_IS_AUTHENTIC,
}

sealed class DocumentScanOcrResult {
    data object Matched : DocumentScanOcrResult()
    data object NotMatched : DocumentScanOcrResult()
}

class ImageContent(val content: ByteArray, val extension: String)

sealed class DocumentScanImage(val face: ImageContent) {
    data class SingleSided(val content: ImageContent) : DocumentScanImage(face = content)
    data class DoubleSided(val front: ImageContent, val back: ImageContent) : DocumentScanImage(face = front)

    fun withType(type: DocumentType): DocumentScanImageWithType {
        return when (this) {
            is DoubleSided -> DocumentScanImageWithType.DoubleSided(type = type, front = front, back = back)
            is SingleSided -> DocumentScanImageWithType.SingleSided(type = type, content = content)
        }
    }
}

sealed class DocumentScanImageWithType(val face: ImageContent) {
    data class SingleSided(val type: DocumentType, val content: ImageContent) : DocumentScanImageWithType(face = content)
    data class DoubleSided(val type: DocumentType, val front: ImageContent, val back: ImageContent) : DocumentScanImageWithType(face = front)
}

sealed class GetImageError(message: String?, cause: Throwable? = null) : Exception(message, cause) {
    data class Incomplete(val documentScanId: DocumentScanId) : GetImageError("Document scan is incomplete")
    data class AccountNotFound(val accountId: AccountId) : GetImageError("Document scan not found for provided account id")
    data class Unexpected(val exception: Throwable) : GetImageError("Unexpected error getting document scan image", exception)
}