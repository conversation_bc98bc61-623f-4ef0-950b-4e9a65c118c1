package ai.friday.billpayment.app.fingerprint

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.DeviceBinded
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.fingerprint.GenerateDeviceResult.AccountClosed
import ai.friday.billpayment.app.fingerprint.GenerateDeviceResult.AccountNotFound
import ai.friday.billpayment.app.fingerprint.GenerateDeviceResult.InvalidFingerprint
import ai.friday.billpayment.app.fingerprint.GenerateDeviceResult.LivenessFailed
import ai.friday.billpayment.app.fingerprint.GenerateDeviceResult.Success
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDateTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Named
import java.util.Base64
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

// FIXME trazer da config
private val ArbiPspInformation = DevicePspInformation(code = "********", agencyCode = "00019")

val INTERNAL_DEVICES_ACCOUNT = AccountId("INTERNAL-DEVICES")

private const val deviceFingerprintLockProvider = "device-fingerprint-lock-provider"

@FridayMePoupe
open class DeviceFingerprintService(
    private val adapter: DeviceFingerprintAdapter,
    private val repository: DeviceFingerprintRepository,
    private val accountService: AccountService,
    private val walletRepository: WalletRepository,
    private val livenessService: LivenessService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val eventPublisher: EventPublisher,
    private val cafService: CafService,
    private val systemActivityService: SystemActivityService,
    @Named(deviceFingerprintLockProvider) private val lockProvider: InternalLock,
) {
    private val logger = LoggerFactory.getLogger(DeviceFingerprintService::class.java)

    open fun isValidFingerprint(deviceFingerprintPayload: String, accountId: AccountId): Boolean {
        val deviceFingerprint = String(Base64.getDecoder().decode(deviceFingerprintPayload))
        val deviceDetails = parseObjectFrom<MobileDeviceDetails>(deviceFingerprint)

        if (deviceDetails.fingerprint == null) {
            logger.warn(Markers.append("accountId", accountId.value), "isValidFingerprint#EmptyFingerprint")
        }

        return deviceDetails.fingerprint
            ?.let { repository.getDeviceOrNull(DeviceFingerprint(it), accountId) }
            ?.let { it.details.fingerprint == deviceDetails.fingerprint && it.status == DeviceStatus.ACTIVE }
            ?: false
    }

    open fun getInternalDevicesAccount() = getOrNull(INTERNAL_DEVICES_ACCOUNT)

    open fun getOrNull(accountId: AccountId): RegisteredDevice? {
        return repository.getByAccountIdOrNull(accountId)
    }

    open fun addDeviceId(accountId: AccountId, walletId: WalletId): Result<AddDeviceIdResult> {
        return kotlin.runCatching {
            val wallet = walletRepository.findWallet(walletId)
            val founder = wallet.founder
            val founderAccount = accountService.findAccountById(founder.accountId)
            val accountNumber = getAccountNumber(walletId)

            val lock = lockProvider.acquireLock(accountId.value + accountNumber.fullAccountNumber) ?: return@runCatching AddDeviceIdResult.Locked

            val device = repository.getByAccountIdOrNull(accountId) ?: return@runCatching AddDeviceIdResult.AccountNotFound

            if (!device.active) {
                return@runCatching AddDeviceIdResult.RegisteredDeviceNotActive
            }

            val deviceId = adapter.createDevice(
                CreateDeviceRequest(
                    deviceDetails = device.details,
                    person = DevicePerson(
                        phoneNumber = MobilePhone(founderAccount.mobilePhone),
                        document = Document(founderAccount.document),
                        fullName = founderAccount.name,
                        email = founderAccount.emailAddress,
                    ),
                    account = DeviceAccountRequest(
                        psp = ArbiPspInformation,
                        accountNumber = accountNumber,
                    ),
                ),
            )

            val registeredDevice = device.copy(
                deviceIds = device.deviceIds + (accountNumber to deviceId),
            )
            repository.save(
                registeredDevice,
                accountId,
            )

            lock.unlock()

            return@runCatching AddDeviceIdResult.Success(registeredDevice)
        }
    }

    private fun createTemporaryDeviceId(accountId: AccountId, walletId: WalletId): DeviceId {
        return createTemporaryDeviceId(accountId, getAccountNumber(walletId))
    }

    private fun createTemporaryDeviceId(accountId: AccountId, accountNumber: AccountNumber): DeviceId {
        val account = accountService.findAccountById(accountId)

        val deviceId = adapter.createDevice(
            CreateDeviceRequest(
                deviceDetails = MobileDeviceDetails(
                    fingerprint = UUID.randomUUID().toString(),
                    uuid = UUID.randomUUID(),
                    alias = "iPhone",
                    screenResolution = DeviceScreenResolution(375, 812),
                    type = DeviceType.IOS,
                    dpi = 3.0,
                    manufacturer = "Apple",
                    model = "iPhone",
                    rooted = false,
                    storageCapacity = *********,
                    osId = UUID.randomUUID().toString().uppercase(),
                    fresh = false,
                ),
                person = DevicePerson(
                    phoneNumber = MobilePhone(account.mobilePhone),
                    document = Document(account.document),
                    fullName = account.name,
                    email = account.emailAddress,
                ),
                account = DeviceAccountRequest(
                    psp = ArbiPspInformation,
                    accountNumber = accountNumber,
                ),
            ),
        )

        return deviceId
    }

    private fun removeTemporaryDevice(deviceId: DeviceId) {
        adapter.removeDeviceId(deviceId)
    }

    open fun <T> withRealOrTemporaryDeviceId(accountId: AccountId, walletId: WalletId, block: (DeviceId) -> T): T {
        val registeredDevice = getOrNull(accountId)
        val accountNumber = getAccountNumberOrNull(walletId)
        val createdTemporaryDevice: Boolean

        val savedDeviceId = registeredDevice?.deviceIds?.get(accountNumber)

        val deviceId = if (savedDeviceId == null) {
            createdTemporaryDevice = true
            createTemporaryDeviceId(accountId = accountId, walletId)
        } else {
            createdTemporaryDevice = false
            savedDeviceId
        }

        try {
            return block(deviceId)
        } catch (e: Exception) {
            logger.error(Markers.append("accountId", accountId.value).andAppend("walletId", walletId.value), "withRealOrTemporaryDeviceId", e)
            throw e
        } finally {
            if (createdTemporaryDevice) {
                removeTemporaryDevice(deviceId)
            }
        }
    }

    open fun removeDeviceId(accountId: AccountId, walletId: WalletId) =
        removeDeviceId(accountId, getAccountNumber(walletId))

    open fun removeDeviceId(accountId: AccountId, accountNumber: AccountNumber): RemoveDeviceIdResult {
        return try {
            val device = repository.getByAccountIdOrNull(accountId) ?: return RemoveDeviceIdResult.RegisteredDeviceNotFound

            val deviceId = device.deviceIds[accountNumber] ?: return RemoveDeviceIdResult.DeviceIdNotFound

            if (adapter.removeDeviceId(deviceId)) {
                device.copy(deviceIds = device.deviceIds - accountNumber).let { repository.save(it, accountId) }
                RemoveDeviceIdResult.Success
            } else {
                RemoveDeviceIdResult.DeviceIdNotRemoved
            }
        } catch (e: Exception) {
            RemoveDeviceIdResult.Unknown(e)
        }
    }

    open fun generateDevice(accountId: AccountId, details: MobileDeviceDetails): GenerateDeviceResult {
        val markers = Markers.append("params.accountId", accountId.value)
            .andAppend("params.details", details)

        val account = accountService.findAccountByIdOrNull(accountId)

        if (account == null) {
            logger.warn(markers, "generateDevice")
            return AccountNotFound
        }

        val accountLivenessId = accountRegisterRepository.findByAccountId(accountId).livenessId

        if (account.closed) {
            logger.warn(markers, "generateDevice")
            return AccountClosed
        }

        if (details.fingerprint == null) {
            logger.warn(markers, "generateDevice")
            return InvalidFingerprint
        }

        val createdPasswordTime = systemActivityService.getCreatedPassword(accountId)
        markers.andAppend("createdPasswordTime", createdPasswordTime?.format(dateTimeFormat))

        val registeredDevices = repository.getByAccountId(accountId)
            .filterNot { it.liveness == null && it.pending } // FIXME remover quando for removido do principal
        markers.andAppend("registeredDevices", registeredDevices)

        if (registeredDevices.isEmpty() && createdPasswordTime != null && createdPasswordTime.isAfter(getLocalDateTime().minusDays(1))) {
            saveFirstDevice(details, account)
            logger.info(markers, "generateDevice")
            return Success(null)
        }

        val hasCompletedEnrollment = accountLivenessId?.provider == LivenessProvider.CAF ||
            livenessService.hasCompletedEnrollment(accountId).getOrElse {
                markers.andAppend("livenessError", it)
                logger.warn(markers, "generateDevice")
                return LivenessFailed(it)
            }

        val livenessId = when {
            accountLivenessId?.provider == LivenessProvider.CAF -> accountLivenessId
            hasCompletedEnrollment -> livenessService.match(accountId).getOrElse {
                markers.andAppend("livenessError", it)
                logger.warn(markers, "generateDevice")
                return LivenessFailed(it)
            }

            else -> livenessService.enroll(accountId).getOrElse {
                markers.andAppend("livenessError", it)
                logger.warn(markers, "generateDevice")
                return LivenessFailed(it)
            }
        }
        repository.save(
            accountId = accountId,
            device = RegisteredDevice(
                deviceIds = mapOf(),
                fingerprint = DeviceFingerprint(details.fingerprint),
                status = DeviceStatus.PENDING,
                creationDate = getZonedDateTime(),
                details = details,
                liveness = DeviceLiveness(id = livenessId, enrollment = !hasCompletedEnrollment),
            ),
        )
        markers.andAppend("livenessId", livenessId.value)
        logger.info(markers, "generateDevice")
        return Success(livenessId)
    }

    open fun confirmDevice(accountId: AccountId, details: MobileDeviceDetails, livenessId: LivenessId): ConfirmDeviceResult {
        if (details.fingerprint == null) {
            return ConfirmDeviceResult.EmptyFingerprint
        }

        val registeredDevice: RegisteredDevice = repository.getDeviceOrNull(
            deviceFingerprint = DeviceFingerprint(details.fingerprint),
            accountId = accountId,
        ) ?: return ConfirmDeviceResult.RegisteredDeviceNotFound

        if (!registeredDevice.pending) {
            return ConfirmDeviceResult.RegisteredDeviceNotPending
        }

        if (registeredDevice.liveness?.id?.value != livenessId.value) {
            return ConfirmDeviceResult.DifferentLivenessId
        }

        if (registeredDevice.details != details) {
            return ConfirmDeviceResult.DeviceDetailsDoNotMatch
        }

        val match = if (registeredDevice.liveness.enrollment) {
            val enrollmentCompleted = if (livenessId.provider == LivenessProvider.CAF) {
                true
            } else {
                livenessService.hasCompletedEnrollment(accountId).getOrElse {
                    return ConfirmDeviceResult.LivenessFailed(it)
                }
            }
            enrollmentCompleted
        } else {
            val matchVerify = if (livenessId.provider == LivenessProvider.CAF) {
                cafService.verifyMatch(livenessId).getOrElse {
                    return ConfirmDeviceResult.LivenessFailed(it)
                }
            } else {
                livenessService.verifyMatch(livenessId).getOrElse {
                    return ConfirmDeviceResult.LivenessFailed(it)
                }
            }
            matchVerify.match
        }

        if (!match) {
            return ConfirmDeviceResult.LivenessDoNotMatch
        }

        inactivateRegisteredDevices(accountId)

        repository.save(
            registeredDevice.copy(status = DeviceStatus.ACTIVE),
            accountId,
        )

        eventPublisher.publish(
            DeviceBinded(
                accountId = accountId,
                deviceFingerprint = DeviceFingerprint(details.fingerprint),
            ),
        )

        return ConfirmDeviceResult.Success
    }

    open fun registerDevices(accountId: AccountId, deviceFingerprint: DeviceFingerprint): RegisterDevicesResult {
        val lock = lockProvider.acquireLock(deviceFingerprint.value) ?: return RegisterDevicesResult.Locked

        val wallets = walletRepository.findWallets(accountId, MemberStatus.ACTIVE)

        val accountNumbers = wallets.mapNotNull {
            val paymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(it.paymentMethodId, it.founder.accountId).method as InternalBankAccount
            if (paymentMethod.bankAccountMode == BankAccountMode.VIRTUAL) {
                null
            } else {
                AccountNumber(paymentMethod.buildFullAccountNumber()) to it.founder.accountId
            }
        }

        val result = addDeviceIds(accountId, deviceFingerprint, accountNumbers)
            .getOrElse { return RegisterDevicesResult.AddDeviceFailure(throwable = it) }

        lock.unlock()

        return when (result) {
            is AddDeviceIdResult.Failure -> RegisterDevicesResult.AddDeviceFailure(result = result)

            is AddDeviceIdResult.Success -> RegisterDevicesResult.Success
        }
    }

    private fun getAccountNumber(walletId: WalletId) = AccountNumber(
        walletRepository.findAccountPaymentMethod(walletId).method
            .let { it as InternalBankAccount }
            .buildFullAccountNumber(),
    )

    private fun getAccountNumberOrNull(walletId: WalletId) = runCatching { getAccountNumber(walletId) }.getOrNull()

    private fun saveFirstDevice(details: MobileDeviceDetails, account: Account) {
        if (details.fingerprint == null) {
            logger.warn(Markers.append("accountId", account.accountId.value), "saveFirstDevice#EmptyFingerprint")
            return
        }

        repository.save(
            accountId = account.accountId,
            device = RegisteredDevice(
                deviceIds = emptyMap(),
                fingerprint = DeviceFingerprint(details.fingerprint),
                status = DeviceStatus.ACTIVE,
                creationDate = getZonedDateTime(),
                details = details,
                liveness = null,
            ),
        )

        eventPublisher.publish(
            DeviceBinded(
                accountId = account.accountId,
                deviceFingerprint = DeviceFingerprint(details.fingerprint),
            ),
        )
    }

    private fun addDeviceIds(accountId: AccountId, deviceFingerprint: DeviceFingerprint, bankAccountInfo: List<Pair<AccountNumber, AccountId>>): Result<AddDeviceIdResult> {
        return runCatching {
            val device = repository.getDeviceOrNull(deviceFingerprint, accountId) ?: return@runCatching AddDeviceIdResult.AccountNotFound

            if (!device.active) {
                return@runCatching AddDeviceIdResult.RegisteredDeviceNotActive
            }

            val deviceIds = bankAccountInfo.map { (accountNumber, founderAccountId) ->
                val account = accountService.findAccountById(founderAccountId)
                val deviceId = adapter.createDevice(
                    CreateDeviceRequest(
                        deviceDetails = device.details,
                        person = DevicePerson(
                            phoneNumber = MobilePhone(account.mobilePhone),
                            document = Document(account.document),
                            fullName = account.name,
                            email = account.emailAddress,
                        ),
                        account = DeviceAccountRequest(
                            psp = ArbiPspInformation,
                            accountNumber = accountNumber,
                        ),
                    ),
                )

                accountNumber to deviceId
            }

            repository.save(
                device.copy(
                    deviceIds = device.deviceIds + deviceIds,
                ),
                accountId,
            )

            return@runCatching AddDeviceIdResult.Success(device)
        }
    }

    private fun inactivateRegisteredDevices(accountId: AccountId) {
        // FIXME remover do cognito também
        repository.getByAccountId(accountId).forEach {
            if (it.status == DeviceStatus.ACTIVE) {
                it.deviceIds.forEach { (accountNumber, _) ->
                    removeDeviceId(accountId, accountNumber)
                }

                repository.save(
                    it.copy(status = DeviceStatus.INACTIVE),
                    accountId,
                )
            }
        }
    }
}

sealed class RegisterDevicesResult : PrintableSealedClassV2() {
    data object Success : RegisterDevicesResult()
    sealed class Failure : RegisterDevicesResult()
    data class AddDeviceFailure(val throwable: Throwable? = null, val result: AddDeviceIdResult.Failure? = null) : Failure()
    data object RegisteredDeviceNotFound : Failure()
    data object Locked : Failure()
}

sealed class GenerateDeviceResult : PrintableSealedClassV2() {
    data class Success(val liveness: LivenessId?) : GenerateDeviceResult()
    sealed class Failure : GenerateDeviceResult()
    data class LivenessFailed(val e: LivenessErrors) : Failure()
    data object AccountNotFound : Failure()
    data object AlreadyActive : Failure()
    data object AccountClosed : Failure()
    data object InvalidFingerprint : Failure()
}

sealed class ConfirmDeviceResult : PrintableSealedClassV2() {
    data object Success : ConfirmDeviceResult()
    sealed class Failure : ConfirmDeviceResult()
    data object RegisteredDeviceNotFound : Failure()
    data object RegisteredDeviceNotPending : Failure()
    data object LivenessDoNotMatch : Failure()
    data object DifferentLivenessId : Failure()
    data class LivenessFailed(val e: LivenessErrors) : Failure()
    data object DeviceDetailsDoNotMatch : Failure()
    data object EmptyFingerprint : Failure()
}

sealed class CreateDeviceResult : PrintableSealedClassV2() {
    data object Success : CreateDeviceResult()
    data object Failure : CreateDeviceResult()
}

sealed class AddDeviceIdResult : PrintableSealedClassV2() {
    data class Success(val registeredDevice: RegisteredDevice) : AddDeviceIdResult()
    sealed class Failure : AddDeviceIdResult()
    data object RegisteredDeviceNotActive : Failure()
    data object AccountNotFound : Failure()
    data object Locked : Failure()
}

sealed class RemoveDeviceIdResult : PrintableSealedClassV2() {
    data object Success : RemoveDeviceIdResult()
    sealed class Failure : RemoveDeviceIdResult()
    data object RegisteredDeviceNotFound : Failure()
    data object DeviceIdNotFound : Failure()
    data object DeviceIdNotRemoved : Failure()
    data class Unknown(val e: Throwable) : Failure()
}