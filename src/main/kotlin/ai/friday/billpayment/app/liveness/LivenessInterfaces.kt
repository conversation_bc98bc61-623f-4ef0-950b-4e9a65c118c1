package ai.friday.billpayment.app.liveness

import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.app.account.AccountId
import arrow.core.Either

data class LivenessId(val value: String, val provider: LivenessProvider = LivenessProvider.FACETEC, val matchLivenessId: String? = null)

enum class LivenessProvider {
    CAF, FACETEC
}

data class LivenessEnrollmentVerification(
    val duplications: Result = Result.NotVerified,
    val fraudIndications: Result = Result.NotVerified,
) {
    val isVerified = duplications is Result.Verified && fraudIndications is Result.Verified

    /**
     * @return Precisa ter sido verificado e ter sido encontrado um ou mais rostos duplicados.
     */
    val isVerifiedWithDuplication = duplications is Result.Verified && duplications.accountIds.isNotEmpty()

    /**
     * @return Precisa ter sido verificado e ter uma ou mais indicações de fraude para o rosto.
     */
    val isVerifiedWithFraudIndication = fraudIndications is Result.Verified && fraudIndications.accountIds.isNotEmpty()

    sealed class Result private constructor() {
        data object NotVerified : Result()
        data class Verified(val accountIds: List<AccountId>) : Result()

        companion object {
            fun create(accountIds: List<AccountId>?): Result {
                return if (accountIds == null) {
                    NotVerified
                } else {
                    return Verified(accountIds)
                }
            }
        }
    }
}

interface LivenessService {
    fun enroll(accountId: AccountId): Either<LivenessErrors, LivenessId>
    fun match(accountId: AccountId): Either<LivenessErrors, LivenessId>
    fun retrieveEnrollmentSelfie(livenessId: LivenessId): Either<LivenessSelfieError, ByteArray>
    fun verifyDuplication(accountId: AccountId): Either<LivenessErrors, LivenessEnrollmentVerification>
    fun verifyMatch(livenessId: LivenessId): Either<LivenessErrors, LivenessMatchVerify>
    fun markAsFraud(accountId: AccountId): Either<LivenessErrors, Unit>
    fun hasCompletedEnrollment(accountId: AccountId): Either<LivenessErrors, Boolean>
}

data class LivenessMatchVerify(
    val livenessId: LivenessId,
    val accountId: AccountId,
    val match: Boolean,
    val attempt: Int,
)

sealed class LivenessErrors : PrintableSealedClass() {
    data object EnrollmentUnavailable : LivenessErrors()
    data object DuplicationCheckUnavailable : LivenessErrors()
    data object MatchUnavailable : LivenessErrors()
    data object AccountNotFound : LivenessErrors()
    data class Error(val e: Exception) : LivenessErrors()
}

sealed class LivenessSelfieError : PrintableSealedClass() {
    data object Unavailable : LivenessSelfieError()
    data class Error(val e: Exception) : LivenessSelfieError()
}