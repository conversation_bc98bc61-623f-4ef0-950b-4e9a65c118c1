package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.OpenFinanceIncentiveType
import ai.friday.billpayment.app.integrations.TestPixReminderType
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import java.util.Base64

sealed interface BillPaymentNotification {
    val accountId: AccountId?
    val template: NotificationTemplate?
    val preferredChannel: BillPaymentNotificationChannel
}

/**
 * Idealmente todas as notificações devem sair por essa classe. Assim o adapter do canal monta o objeto específico para o canal.
 */
data class MultiChannelNotification(
    override val preferredChannel: BillPaymentNotificationChannel,
    val receiver: Account,
    val walletId: WalletId,
    val notificiationType: NotificationType, // TODO - avaliar se ainda é necessário e como usar melhor esse campo
    val configurationKey: MultiChannelConfigurationKey,
    val parameters: Map<String, String> = emptyMap(),
    val actionButton: ButtonParameter?,
    val media: NotificationMedia? = null,
) : BillPaymentNotification {
    override val template = null
    override val accountId = receiver.accountId
}

data class EmailNotification(
    override val preferredChannel: BillPaymentNotificationChannel = BillPaymentNotificationChannel.EMAIL,
    val receiver: EmailAddress,
    override val accountId: AccountId? = null,
    override val template: NotificationTemplate,
    val parameters: Map<String, String>,
) : BillPaymentNotification

@Deprecated("use MultiChannelNotification instead")
data class WhatsappNotification(
    override val preferredChannel: BillPaymentNotificationChannel = BillPaymentNotificationChannel.CHATBOT,
    val receiver: MobilePhone,
    override val accountId: AccountId? = null,
    override val template: NotificationTemplate,
    val configurationKey: String?,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int? = null,
    val buttonWhatsAppParameter: ButtonParameter? = null,
    val media: NotificationMedia? = null,
) : BillPaymentNotification {
    constructor(
        account: Account,
        template: NotificationTemplate,
        configurationKey: String?,
        parameters: List<String> = listOf(),
        quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
        quickRepliesStartIndex: Int? = null,
        buttonParameter: ButtonParameter? = null,
        media: NotificationMedia? = null,
    ) : this(
        receiver = MobilePhone(account.mobilePhone),
        accountId = account.accountId,
        template = template,
        configurationKey = configurationKey,
        parameters = parameters,
        quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter,
        quickRepliesStartIndex = quickRepliesStartIndex,
        buttonWhatsAppParameter = buttonParameter,
        media = media,
    )
}

sealed class ChatbotNotification(
    open val walletId: WalletId,
    open val details: ChatBotNotificationDetails,
) : BillPaymentNotification {
    override val preferredChannel: BillPaymentNotificationChannel = BillPaymentNotificationChannel.CHATBOT
    override val template = null
}

data class ChatbotNotificationWithAccount(
    override val walletId: WalletId,
    val account: Account,
    override val details: ChatBotNotificationDetails,
) : ChatbotNotification(
    walletId = walletId,
    details = details,

) {
    override val accountId: AccountId = account.accountId
}

data class ChatbotNotificationWithPartialAccount(
    override val walletId: WalletId,
    val partialAccount: PartialAccount,
    val register: AccountRegisterData,
    override val details: ChatBotNotificationDetails,
) : ChatbotNotification(
    walletId = walletId,
    details = details,
) {
    override val accountId: AccountId = partialAccount.id
}

sealed interface ChatBotNotificationDetails

data class BillComingDueRegularDetails(
    val bills: List<BillView>,
    val wallet: Wallet,
) : ChatBotNotificationDetails

data class BillComingDueLastWarnDetails(
    val bills: List<BillView>,
    val wallet: Wallet,
    val hint: String?,
) : ChatBotNotificationDetails

data class WelcomeDetails(
    val type: WelcomeMessageType,
) : ChatBotNotificationDetails

data class PixTransactionDetails(
    val transactionId: String,
    val recipientName: String,
    val recipientDocument: String,
    val recipientInstitution: String,
) : ChatBotNotificationDetails

data class TestPixreminderDetails(
    val bills: List<BillView>,
    val wallet: Wallet,
    val reminderType: TestPixReminderType,
) : ChatBotNotificationDetails

data class OpenFinanceIncentiveDetails(
    val type: OpenFinanceIncentiveType,
    val userOptedOut: Boolean,
    val bank: String? = null,
    val billInfo: Pair<Wallet, BillView>?,
) : ChatBotNotificationDetails

data class UpdateAccountStatus(
    val accountId: AccountId,
    val msisdn: String,
    val paymentStatus: AccountPaymentStatus,
    val status: AccountStatus,
) : ChatBotNotificationDetails

data object RegisterCompleted : ChatBotNotificationDetails

data class NotificationTemplate(val value: String)

sealed interface ButtonParameter {
    val value: String
}

data class ButtonDeeplinkParameter(private val path: String) : ButtonParameter {
    override val value = "app/$path"
}

data class ButtonTrackedDeeplinkParameter(private val path: String, private val event: String) : ButtonParameter {
    override val value = "app/track/$event/${String(encoder.encode("/$path".toByteArray()))}"

    companion object {
        private val encoder = Base64.getUrlEncoder()
    }
}

data class ButtonWebParameter(private val path: String) : ButtonParameter {
    override val value = "web/$path"
}

data class ButtonRawParameter(private val path: String) : ButtonParameter {
    override val value = path
}

sealed class NotificationMedia(val type: NotificationMediaType) {
    data class Document(val url: String, val filename: String, val documentType: String?) : NotificationMedia(NotificationMediaType.DOCUMENT)
    data class Image(val url: String, val imageType: String?, val title: String? = null, val text: String? = null) : NotificationMedia(NotificationMediaType.IMAGE)
    data class Video(val url: String, val videoType: String?) : NotificationMedia(NotificationMediaType.VIDEO)
}

enum class NotificationMediaType {
    DOCUMENT, IMAGE, VIDEO
}