package ai.friday.billpayment.app.notification

enum class NotificationType {
    BILL_CREATED,
    B<PERSON>L_COMING_DUE,
    ONE_PIX_PAY,
    EMAIL_NOT_PROCESSED,
    CASH_IN_FAILURE,
    ADD_FAILURE,
    TRANSFER_NOT_PAYABLE,
    BILL_COMING_DUE_LAST_WARN,
    BIL<PERSON>_COMING_DUE_SECONDARY_WALLET,
    BILL_OVERDUE_YESTERDAY,
    USER_ACTIVATED,
    UPGRADE_COMPLETED,
    WALLET_MEMBER_JOINED,
    USER_AUTHENTICATION_REQUIRED,
    REGISTER_COMPLETED,
    REGISTER_UPDATED,
    INVOICE_PAYMENT_UNDONE,
    B<PERSON>ETO_RECEIPT,
    INVOICE_RECEIPT,
    PIX_RECEIPT,
    INVESTMENT_RECEIPT,
    INVESTMENT_REDEMPTION_CREATED,
    INVESTMENT_VALUE_OPTIMIZED,
    INVESTMENT_REDEMPTION_COMPLETED,
    INVESTMENT_REDEMPTION_FAILED,
    INVESTMENT_REDEMPTION_PARTIAL_FAILED,
    GOAL_COMPLETED,
    CASH_IN,
    CASH_IN_PAYMENT_INSUFFICIENT_BALANCE_TODAY,
    CASH_IN_PAYMENT_SUFFICIENT_BALANCE_TODAY,
    CASH_IN_PAYMENT_SUFFICIENT_BALANCE_TODAY_OWNER,
    SCHEDULED_BILL_NOT_PAYABLE,
    BILL_SCHEDULE_EXPIRED,
    INSUFFICIENT_BALANCE_TODAY,
    INSUFFICIENT_BALANCE_TODAY_SECONDARY_WALLET,
    BILL_SCHEDULE_POSTPONED_DUE_LIMIT_REACHED,
    BILL_SCHEDULE_CANCELED_DUE_AMOUNT_HIGHER_THAN_DAILY_LIMIT,
    BILL_SCHEDULE_CANCELED_DUE_AMOUNT_CHANGED,
    BILL_SCHEDULE_CANCELED_DUE_CREDIT_CARD_DENIED,
    NOT_VISIBLE_BILL_ALREADY_EXISTS,
    INVITED_MEMBER,
    INVITE_REMINDER,
    FIRST_BILL_SCHEDULED,
    OPT_OUT,
    ONE_PIX_PAY_FAILURE,
    ONE_PIX_PAY_LIMIT_EXCEEDED,
    SUBSCRIPTION_CREATED,
    SUBSCRIPTION_GRANTED_BY_INVESTMENT,
    SUBSCRIPTION_OVERDUE,
    PAYMENT_INTENT_FAILED,
    BASIC_SIGN_UP_UPDATE_DATA_NEEDED,
    CREDIT_CARD_ENABLED,
    TOKEN,
    ACCOUNT,
    PIX_NOT_RECEIVED_FAILURE,
    UTILITY_ACCOUNT_UPDATED_STATUS,
    UTILITY_ACCOUNT_DISCONNECT_LEGACY_ACCOUNT,
    UTILITY_ACCOUNT_INVOICES_NOT_FOUND,
    UTILITY_ACCOUNT_INVOICES_SCAN_ERROR,
    INSECURE_BILL,
    REGISTER_DENIED,
    CHANGE_PAYMENT_METHOD,
    NON_RETRYABLE_PAYMENT_FAILURE,
    TRI_PIX_REMINDER,
    TRI_PIX_NEXT_DAY_REMINDER,
    TRI_PIX_LAST_DAY_REMINDER,
    TRI_PIX_EXPIRED,
    BILL_BATCH_SCHEDULE_RESULT,
    BILL_SCHEDULE_CANCEL_RESULT,
    MANUAL_ENTRY_REMINDER,
    COUPON_MANUAL_RESEND,
    SWEEPING_ACCOUNT_CONNECTED,
    SWEEPING_ACCOUNT_CONNECTED_WITH_DATA_INCENTIVE,
    SWEEPING_ACCOUNT_EDIT,
    SWEEPING_ACCOUNT_CONNECTION_FAILED,
    SWEEPING_ACCOUNT_EDIT_FAILED,
    DATA_CONSENT_CONNECTED,
    DATA_CONSENT_CONNECTION_FAILED,
    SWEEPING_TRANSACTION_FAILED,
    FREE_NOTIFICATION,
    WELCOME_MESSAGE,
    OPEN_FINANCE_INCENTIVE,
    OPEN_FINANCE_DDA_INCENTIVE,
    PIX_KEY_FIND_RESULT,
    PIX_INFRACTION_VALUE_BLOCKED,
    PIX_INFRACTION_VALUE_RELEASED,
    PIX_INFRACTION_VALUE_RETURNED_TO_OTHER_PAYER,
    PIX_INFRACTION_VALUE_REFUNDED,
}

data class ByteArrayWithNameAndType(
    val fileName: String,
    val mediaType: String,
    val data: ByteArray,
) {
    // Equals customizado pra comparar o array
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ByteArrayWithNameAndType

        if (fileName != other.fileName) return false
        if (mediaType != other.mediaType) return false
        return data.contentEquals(other.data)
    }
}