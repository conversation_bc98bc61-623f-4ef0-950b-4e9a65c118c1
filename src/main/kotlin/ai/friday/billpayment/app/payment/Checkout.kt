package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource.WalletActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CheckableSettlementStatus
import ai.friday.billpayment.app.integrations.NewPixCommand
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.pix.PixErrorType
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
class BalancePixCheckout(
    private val pixPaymentService: PixPaymentService,
    @Property(name = "integrations.arbi.pixCheckoutWaitTime") private val waitTime: Long,
    @Property(name = "integrations.arbi.pixCheckoutPoolingInterval") private val poolingInterval: Long,
    private val pixKeyManagement: PixKeyManagement,
    private val transactionService: TransactionService,
    private val accountRepository: AccountRepository,
    private val walletService: WalletService,
    private val deviceFingerprintService: DeviceFingerprintService,
) : SyncCheckout, CheckableSettlementStatus {
    override fun execute(transaction: Transaction): Transaction {
        val bankOperationId = BankOperationId(transaction.id.value)

        val transactionAlreadyStarted = (transaction.paymentData.toSingle().payment as? BalanceAuthorization)?.operationId != null

        if (transactionAlreadyStarted) {
            return transaction
        }

        try {
            val pixPaymentResult = startPayment(transaction, bankOperationId)

            if (pixPaymentResult.status == PixPaymentStatus.REFUSED) {
                return handlePixRefused(pixPaymentResult, transaction, bankOperationId)
            }

            if (pixPaymentResult.status == PixPaymentStatus.FAILED) {
                return handlePixFailed(pixPaymentResult, transaction)
            }

            val statusResult = checkPaymentStatusManyTimes(
                method = transaction.paymentData.toSingle().accountPaymentMethod.method as InternalBankAccount,
                bankOperationId = bankOperationId,
                poolingInterval = poolingInterval,
                maxWaitTime = waitTime,
                waitForStatuses = listOf(PixPaymentStatus.FAILED, PixPaymentStatus.REFUSED, PixPaymentStatus.SUCCESS),
            )

            when (statusResult.status) {
                PixPaymentStatus.FAILED, PixPaymentStatus.REFUSED -> {
                    val error = statusResult.error ?: PixTransactionError.UnknownTemporaryError
                    return applySettlementError(
                        transaction,
                        pixPaymentResult,
                        if (error.type == PixErrorType.TEMPORARY) BankOperationStatus.ERROR else BankOperationStatus.INVALID_DATA,
                        error,
                    )
                }

                PixPaymentStatus.SUCCESS -> {
                    val bankTransfer = BankTransfer(
                        operationId = bankOperationId,
                        gateway = FinancialServiceGateway.ARBI,
                        status = BankOperationStatus.SUCCESS,
                        amount = transaction.settlementData.totalAmount,
                        authentication = statusResult.endToEnd,
                        pixKeyDetails = pixPaymentResult.pixKeyDetails,
                    )
                    return transaction.apply {
                        settlementData.settlementOperation =
                            bankTransfer // Não seria melhor diferenciar pix de transferencia?
                        status = TransactionStatus.COMPLETED
                        paymentData.toSingle().payment = BalanceAuthorization(
                            operationId = bankTransfer.operationId,
                            status = BankOperationStatus.SUCCESS,
                            amount = transaction.settlementData.totalAmount,
                            paymentGateway = FinancialServiceGateway.ARBI,
                        )
                    }
                }

                else -> {
                    val bankTransfer = BankTransfer(
                        operationId = bankOperationId,
                        gateway = FinancialServiceGateway.ARBI,
                        status = BankOperationStatus.UNKNOWN,
                        amount = transaction.settlementData.totalAmount,
                        authentication = pixPaymentResult.endToEnd,
                    )
                    return transaction.apply {
                        settlementData.settlementOperation =
                            bankTransfer // Não seria melhor diferenciar pix de transferencia?
                        paymentData.toSingle().payment = BalanceAuthorization(
                            operationId = bankTransfer.operationId,
                            status = BankOperationStatus.SUCCESS,
                            amount = transaction.settlementData.totalAmount,
                            paymentGateway = FinancialServiceGateway.ARBI,
                        )
                    }
                }
            }
        } catch (e: Exception) {
            LOG.error(append("transactionId", transaction.id.value), "BalancePixCheckout", e)
            return transaction
        }
    }

    private fun handlePixFailed(
        pixPaymentResult: PixPaymentResult,
        transaction: Transaction,
    ): Transaction {
        val error = pixPaymentResult.error!!
        return applySettlementError(
            transaction,
            pixPaymentResult,
            if (error.type == PixErrorType.TEMPORARY) BankOperationStatus.ERROR else BankOperationStatus.INVALID_DATA,
            error,
        )
    }

    private fun handlePixRefused(
        pixPaymentResult: PixPaymentResult,
        transaction: Transaction,
        bankOperationId: BankOperationId,
    ): Transaction = when {
        pixPaymentResult.error is PixTransactionError.PaymentGenericTemporaryError -> {
            transaction.apply {
                status = TransactionStatus.FAILED
                paymentData.toSingle().payment = BalanceAuthorization(
                    operationId = bankOperationId,
                    status = BankOperationStatus.INVALID_DATA,
                    amount = transaction.settlementData.totalAmount,
                    paymentGateway = FinancialServiceGateway.ARBI,
                )
            }
        }

        pixPaymentResult.error?.type == PixErrorType.PERMANENT -> {
            applySettlementError(
                transaction,
                pixPaymentResult,
                BankOperationStatus.INVALID_DATA,
                pixPaymentResult.error,
            )
        }

        else -> {
            applySettlementError(
                transaction,
                pixPaymentResult,
                BankOperationStatus.ERROR,
                pixPaymentResult.error!!,
            )
        }
    }

    private fun startPayment(transaction: Transaction, bankOperationId: BankOperationId): PixPaymentResult {
        val bill = transaction.settlementData.getTarget<Bill>()

        val isBankAccountPix = bill.recipient!!.bankAccount != null

        val originBankAccount = transaction.paymentData.toSingle().accountPaymentMethod.method as InternalBankAccount

        val scheduledBy = bill.scheduledBy ?: (bill.scheduleSource as? WalletActionSource)?.accountId ?: (bill.source as WalletActionSource).accountId ?: walletService.findWallet(bill.walletId).founder.accountId

        val deviceId = scheduledBy.let { deviceFingerprintService.getOrNull(scheduledBy)?.deviceIds?.get(AccountNumber(originBankAccount.buildFullAccountNumber())) }

        val account = accountRepository.findById(transaction.payer.accountId)

        val command = if (isBankAccountPix) {
            NewPixCommand.BankAccountCommand(
                payerName = account.name,
                recipientName = bill.recipient!!.name,
                recipientDocument = bill.recipient!!.document!!,
                originBankAccount = originBankAccount,
                description = bill.description,
                amount = bill.amountTotal,
                bankOperationId = bankOperationId,
                recipientBankAccount = bill.recipient!!.bankAccount!!,
            )
        } else {
            val pixPaymentResult = pixKeyManagement.findKeyDetails(
                key = bill.recipient!!.pixKeyDetails!!.key,
                document = transaction.payer.document,
            ).getOrElse { pixKeyError -> return handleFindPixKeyError(pixKeyError, bankOperationId, transaction) }

            NewPixCommand.PixKeyCommand(
                payerName = account.name,
                recipientName = bill.recipient!!.name,
                recipientDocument = bill.recipient!!.document!!,
                originBankAccount = originBankAccount,
                description = bill.description,
                amount = bill.amountTotal,
                pixQrCodeId = bill.pixQrCodeData?.pixId,
                bankOperationId = bankOperationId,
                endToEnd = pixPaymentResult.e2e,
                pixKeyDetails = pixPaymentResult.pixKeyDetails,
            )
        }

        transaction.apply {
            settlementData.settlementOperation = BankTransfer(
                operationId = bankOperationId,
                gateway = FinancialServiceGateway.ARBI,
                status = BankOperationStatus.UNKNOWN, // TODO: faz sentido ser unkown?
                amount = transaction.settlementData.totalAmount,
                authentication = "",
                pixKeyDetails = (command as? NewPixCommand.PixKeyCommand)?.pixKeyDetails,
            )
            status = TransactionStatus.PROCESSING
            paymentData.toSingle().payment = BalanceAuthorization(
                operationId = bankOperationId,
                status = BankOperationStatus.SUCCESS,
                amount = transaction.settlementData.totalAmount,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        }

        transactionService.save(transaction)

        return pixPaymentService.transfer(command, deviceId)
    }

    private fun handleFindPixKeyError(
        pixKeyError: PixKeyError,
        bankOperationId: BankOperationId,
        transaction: Transaction,
    ): PixPaymentResult = when (pixKeyError) {
        is PixKeyError.UnknownError, PixKeyError.KeyNotConfirmed -> {
            val result = PixPaymentResult(pixKeyDetails = null, status = PixPaymentStatus.REFUSED, idOrdemPagamento = bankOperationId.value, endToEnd = "", error = PixTransactionError.SettlementGenericTemporaryError)
            applySettlementError(
                transaction,
                result,
                BankOperationStatus.INVALID_DATA,
                PixTransactionError.SettlementGenericTemporaryError,
            )
            result
        }

        is PixKeyError.SystemUnavailable -> {
            val result = PixPaymentResult(pixKeyDetails = null, status = PixPaymentStatus.REFUSED, idOrdemPagamento = bankOperationId.value, endToEnd = "", error = PixTransactionError.SystemUnavailable)
            applySettlementError(
                transaction,
                result,
                BankOperationStatus.INVALID_DATA,
                PixTransactionError.SystemUnavailable,
            )
            result
        }

        PixKeyError.KeyNotFound,
        PixKeyError.MalformedKey,
        PixKeyError.InvalidQrCode,
        -> {
            val result = PixPaymentResult(
                status = PixPaymentStatus.REFUSED,
                idOrdemPagamento = bankOperationId.value,
                endToEnd = "",
                error = PixTransactionError.InvalidPixkey,
            )
            applySettlementError(
                transaction,
                result,
                BankOperationStatus.INVALID_DATA,
                PixTransactionError.SettlementGenericTemporaryError,
            )
            result
        }
    }

    private fun applySettlementError(
        transaction: Transaction,
        pixPaymentResult: PixPaymentResult,
        settlementStatus: BankOperationStatus,
        error: PixTransactionError,
    ): Transaction {
        return transaction.apply {
            status = TransactionStatus.FAILED
            paymentData.toSingle().payment = BalanceAuthorization(
                operationId = BankOperationId(pixPaymentResult.idOrdemPagamento),
                status = BankOperationStatus.SUCCESS,
                amount = transaction.settlementData.totalAmount,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
            settlementData.settlementOperation = BankTransfer(
                operationId = BankOperationId(pixPaymentResult.idOrdemPagamento),
                gateway = FinancialServiceGateway.ARBI,
                amount = transaction.settlementData.totalAmount,
                status = settlementStatus,
                errorDescription = error.code,
                authentication = pixPaymentResult.endToEnd,
            )
        }
    }

    override fun rollbackTransaction(transaction: Transaction): Transaction {
        transaction.rollback()
        return transaction
    }

    override fun checkSettlementStatus(transaction: Transaction): SettlementStatus {
        val statusResult =
            pixPaymentService.checkPaymentStatus(
                transaction.paymentData.toSingle().accountPaymentMethod.method as InternalBankAccount,
                (transaction.settlementData.settlementOperation as BankTransfer).operationId,
            )
        return when (statusResult.status) {
            PixPaymentStatus.FAILED, PixPaymentStatus.REFUSED -> {
                val error = statusResult.error ?: PixTransactionError.UnknownTemporaryError
                applySettlementError(
                    transaction,
                    statusResult,
                    if (error.type == PixErrorType.TEMPORARY) BankOperationStatus.ERROR else BankOperationStatus.INVALID_DATA,
                    error,
                )
                SettlementStatus.Failure(error.code)
            }

            PixPaymentStatus.ACKNOWLEDGED -> SettlementStatus.Error
            PixPaymentStatus.SUCCESS -> SettlementStatus.Success()
            else -> {
                LOG.error(
                    append("PixPaymentResult", statusResult).and(append("transactionId", transaction.id.value)),
                    "BalancePixCheckoutCheckSettlementStatus",
                )
                SettlementStatus.Error
            }
        }
    }

    private fun checkPaymentStatusManyTimes(
        method: InternalBankAccount,
        bankOperationId: BankOperationId,
        poolingInterval: Long,
        maxWaitTime: Long,
        waitForStatuses: List<PixPaymentStatus>,
    ): PixPaymentResult {
        val startTime = getEpochMilli()
        val markers = append("bankOperationId", bankOperationId).andAppend("poolingInterval", poolingInterval)
            .andAppend("maxWaitTime", maxWaitTime)
        while (true) {
            Thread.sleep(poolingInterval)
            val statusResult = pixPaymentService.checkPaymentStatus(
                method,
                bankOperationId,
            )
            val elapsedTime = getEpochMilli() - startTime
            if (waitForStatuses.contains(statusResult.status)) {
                LOG.info(
                    markers.andAppend("status", statusResult).andAppend("elapsedTime", elapsedTime)
                        .andAppend("statusMatch", true),
                    "BalancePixCheckout",
                )
                return statusResult
            }
            if (maxWaitTime < elapsedTime) {
                LOG.info(
                    markers.andAppend("status", statusResult).andAppend("elapsedTime", elapsedTime)
                        .andAppend("statusMatch", false),
                    "BalancePixCheckout",
                )
                return statusResult
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BalancePixCheckout::class.java)
    }
}

interface InvestmentCheckout : SyncCheckout, CheckableSettlementStatus

sealed interface Checkout {
    fun execute(transaction: Transaction): Transaction
    fun rollbackTransaction(transaction: Transaction): Transaction
    fun supportsRetryTransaction(): Boolean
}

interface AsyncSettlementCheckout : Checkout {
    fun continueSettlement(transaction: Transaction, settlementOperation: SettlementOperation): Transaction
    override fun supportsRetryTransaction() = true
}

interface SyncCheckout : Checkout {
    override fun supportsRetryTransaction() = true
}

interface AsyncCheckout : Checkout {
    fun continueTransaction(transaction: Transaction): Transaction
    override fun supportsRetryTransaction() = false
}