package ai.friday.billpayment.app.payment.captureFunds

import ai.friday.billpayment.app.payment.FraudPreventionPaymentOperationDenied
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.Transaction

interface CaptureFundsLocator {
    fun checkOnFraudPrevention(transaction: Transaction): FraudPreventionPaymentOperationDenied?
    fun captureFunds(transaction: Transaction): PaymentOperation
    fun undoCaptureFunds(transaction: Transaction, isRefund: Boolean = false): PaymentOperation?
    fun updatePaymentStatus(transaction: Transaction)
}