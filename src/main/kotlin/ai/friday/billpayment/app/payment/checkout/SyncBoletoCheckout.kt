package ai.friday.billpayment.app.payment.checkout

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.CheckableSettlementStatus
import ai.friday.billpayment.app.integrations.UpdateablePaymentStatus
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.SyncCheckout
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.captureFunds.CaptureFundsLocator
import ai.friday.morning.log.andAppend
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class SyncBoletoCheckout(
    private val captureFundsLocator: CaptureFundsLocator,
    private val boletoSettlementService: BoletoSettlementService,
) : SyncCheckout, CheckableSettlementStatus, UpdateablePaymentStatus {
    private val cancelableStatus =
        listOf(BoletoSettlementStatus.AUTHORIZED, BoletoSettlementStatus.CONFIRMED, BoletoSettlementStatus.UNKNOWN)

    override fun execute(transaction: Transaction): Transaction {
        val operation = transaction.settlementData.getOperation<BoletoSettlementResult>()
        captureFundsLocator.checkOnFraudPrevention(transaction)?.let {
            transaction.status = TransactionStatus.FAILED
            return transaction
        }
        val paymentResponse = boletoSettlementService.initPayment(
            bill = transaction.settlementData.getTarget(),
            nsu = transaction.nsu.toInt(),
            transactionId = operation.bankTransactionId,
            payerDocument = transaction.payer.document,
            payerName = transaction.payer.name,
        )
        operation.init(
            status = paymentResponse.status,
            bankTransactionId = paymentResponse.transactionId.toString(),
            errorDescription = paymentResponse.message,
        )
        if (operation.status != BoletoSettlementStatus.AUTHORIZED) {
            return rollbackTransaction(transaction)
        }
        try {
            val paymentOperation = captureFundsLocator.captureFunds(transaction)
            when (paymentOperation.status()) {
                PaymentStatus.SUCCESS -> {
                    boletoSettlementService.confirmPayment(
                        transaction.payer.accountId.value,
                        transaction.nsu.toInt(),
                        operation.bankTransactionId,
                    )
                    transaction.settlementData.getOperation<BoletoSettlementResult>().confirm()
                    transaction.status = TransactionStatus.COMPLETED
                }

                PaymentStatus.ERROR -> {
                    boletoSettlementService.cancelPayment(
                        transaction.payer.accountId.value,
                        transaction.nsu.toInt(),
                        operation.bankTransactionId,
                    )
                    transaction.settlementData.getOperation<BoletoSettlementResult>().void()
                    transaction.status = TransactionStatus.FAILED
                }

                PaymentStatus.REFUNDED, PaymentStatus.AUTHORIZED -> {
                    logger.error(
                        Markers.append("ACTION", "VERIFY").andAppend("transaction", transaction).andAppend(
                            "description",
                            "não deveria acontecer um pagamento REFUNDED ou AUTHORIZED nesse momento",
                        ),
                        "BoletoCheckout#execute",
                    )
                }

                PaymentStatus.UNKNOWN -> {}
            }
            return transaction
        } catch (e: BoletoSettlementException) {
            transaction.settlementData.getOperation<BoletoSettlementResult>().errorDescription = e.message
            return rollbackTransaction(transaction)
        } catch (e: Exception) {
            return rollbackTransaction(transaction)
        }
    }

    override fun rollbackTransaction(transaction: Transaction): Transaction {
        val markers = Markers.append("transactionId", transaction.id.value)
        try {
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                if (status in cancelableStatus) {
                    val currentStatus = boletoSettlementService.queryPayment(bankTransactionId)
                    if (currentStatus == BoletoSettlementStatus.CONFIRMED) {
                        throw IllegalStateException("Boleto já está CONFIRMED. Não pode fazer rollback")
                    }

                    boletoSettlementService.cancelPayment(externalTerminal, externalNsu.toInt(), bankTransactionId)
                    void()
                }
            }
            val paymentOperation = captureFundsLocator.undoCaptureFunds(transaction, isRefund = false)
            paymentOperation?.let {
                if (it.status() == PaymentStatus.ERROR) {
                    logger.warn(
                        markers.andAppend("resultStatus", paymentOperation.status()),
                        "BoletoRollbackTransaction",
                    )
                    return transaction
                }
                if (it.status() == PaymentStatus.UNKNOWN) {
                    logger.error(
                        markers.andAppend("ACTION", "VERIFY").andAppend("undoCaptureFundsResult", it),
                        "BoletoRollbackTransaction",
                    )
                }
            }
            logger.info(markers, "BoletoRollbackTransaction")
            return transaction.apply { rollback() }
        } catch (e: Exception) {
            logger.warn(markers, "BoletoRollbackTransaction", e)
            return transaction
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SyncBoletoCheckout::class.java)
    }

    override fun checkSettlementStatus(transaction: Transaction): SettlementStatus {
        return when (
            val status =
                boletoSettlementService.queryPayment(transaction.settlementData.getOperation<BoletoSettlementResult>().bankTransactionId)
        ) {
            BoletoSettlementStatus.CONFIRMED -> SettlementStatus.Success()
            else -> SettlementStatus.Failure(status.name)
        }
    }

    override fun updatePaymentStatus(transaction: Transaction) {
        captureFundsLocator.updatePaymentStatus(transaction)
    }
}