package ai.friday.billpayment.app.reports

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BonusCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCredit
import ai.friday.billpayment.app.integrations.CustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCreditReason
import ai.friday.billpayment.app.integrations.CustomerAccountCreditRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.RefundedBillCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.RefundedSubscriptionCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.TransactionRollbackCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.toAmountFormat
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.LocalDate
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val arbiBankNo = 213L

@FridayMePoupe
class BankAccountReconciliationReportService(
    private val accountRepository: AccountRepository,
    private val arbiAdapter: BankAccountService,
    private val accountStatementAdapter: AccountStatementAdapter,
    private val billRepository: BillRepository,
    private val walletRepository: WalletRepository,
    private val emailSender: EmailSenderService,
    private val messagePublisher: MessagePublisher,
    private val customerAccountCreditRepository: CustomerAccountCreditRepository,
    @Property(name = "friday.morning.messaging.consumer.account-reconciliation-report.queueName")private val accountReconciliationReportQueueName: String,
) {
    @field:Property(name = "integrations.arbi.inscricao")
    lateinit var arbiSettlementAccountDocument: String

    @field:Property(name = "integrations.arbi.contaLiquidacao")
    lateinit var arbiSettlementAccountNo: String

    @field:Property(name = "integrations.arbi.contaCashin")
    lateinit var arbiCashInAccountNo: String

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "internalBankService.checkStatus.endDatePlusDays")
    var endDatePlusDays: Long = 0

    private val subject = "Relatório de Conciliação de Contas Arbi e Conta Liquidação"

    private fun listAllEligibleAccounts(): List<AccountPaymentMethod> =
        accountRepository
            .findAllPhysicalBalanceAccount()
            .filter { it.isEligible() }

    private fun AccountPaymentMethod.isEligible(): Boolean =
        this.status == AccountPaymentMethodStatus.ACTIVE &&
            (this.method as? InternalBankAccount)?.buildFullAccountNumber()?.toLong() != arbiSettlementAccountNo.toLong()

    fun checkIsEligible(paymentMethod: AccountPaymentMethod): Boolean = paymentMethod.isEligible()

    fun generateArbiAccountsReportAsync(
        date: LocalDate,
        cutOffTime: Int,
    ): Int {
        val messages =
            listAllEligibleAccounts().map {
                AccountReconciliationReportQueueMessageTO(
                    date = date.format(dateFormat),
                    cutOffTime = cutOffTime,
                    accountId = it.accountId.value,
                    accountPaymentMethodId = it.id.value,
                    retry = 0,
                )
            }

        messagePublisher.sendMessageBatch(
            messageBatch =
            QueueMessageBatch(
                queueName = accountReconciliationReportQueueName,
                messages = messages.map { getObjectMapper().writeValueAsString(it) },
                delaySeconds = null,
            ),
        )

        return messages.size
    }

    private fun canCheckAgain(previouslyCheckedTimes: Int): Boolean = previouslyCheckedTimes < 4

    fun retryGenerateArbiAccountsReportAsync(
        date: LocalDate,
        cutOffTime: Int,
        accountPaymentMethod: AccountPaymentMethod,
        previouslyCheckedTimes: Int,
    ): Boolean {
        if (!canCheckAgain(previouslyCheckedTimes)) {
            return false
        }

        val message =
            AccountReconciliationReportQueueMessageTO(
                date = date.format(dateFormat),
                cutOffTime = cutOffTime,
                accountId = accountPaymentMethod.accountId.value,
                accountPaymentMethodId = accountPaymentMethod.id.value,
                retry = previouslyCheckedTimes + 1,
            )

        messagePublisher.sendMessage(
            QueueMessage(
                queueName = accountReconciliationReportQueueName,
                jsonObject = getObjectMapper().writeValueAsString(message),
                delaySeconds = 10 * 60, // 10 minutos
            ),
        )
        return true
    }

    fun generateArbiAccountsReport(
        date: LocalDate,
        cutOffTime: Int,
        accountPaymentMethod: AccountPaymentMethod,
        previouslyCheckedTimes: Int,
    ) = accountPaymentMethod.checkDivergences(date, cutOffTime, previouslyCheckedTimes)

    private fun getCurrentWindowBillsRefunded(
        accountPaymentMethodId: AccountPaymentMethodId,
        initialDate: ZonedDateTime,
        cutOffTime: Int,
    ) = customerAccountCreditRepository.findByCreditDate(
        accountPaymentMethodId = accountPaymentMethodId,
        startDate = initialDate,
        endDate = initialDate.plusDays(1),
    ).filter {
        it.createdAt.hour < cutOffTime
    }.map {
        it.details
    }.filterIsInstance<RefundedBillCustomerAccountCreditDetails>()

    private fun AccountPaymentMethod.checkDivergences(
        date: LocalDate,
        cutOffTime: Int,
        previouslyCheckedTimes: Int,
    ): ArbiAccountReportSummary {
        fun InternalBankAccount.buildArbiAccountReportSummary(
            debitsConciliation: ArbiAccountConciliation = ArbiAccountConciliation.ZERO,
            creditsConciliation: ArbiAccountConciliation = ArbiAccountConciliation.ZERO,
        ) = ArbiAccountReportSummary(
            accountId = accountId,
            accountNumber = accountNumber,
            date = date,
            cutOffTime = cutOffTime,
            debitsConciliation = debitsConciliation,
            refundsConciliation = creditsConciliation,
        )

        val wholeDayWindow = (cutOffTime == 24)

        val logName = if (wholeDayWindow) {
            "BankAccountReconciliationReportService#finalReport"
        } else {
            "BankAccountReconciliationReportService#temporaryReport"
        }
        val markers = Markers.append("params.accountId", accountId.value)
            .andAppend("params.cutoffTime", cutOffTime)
            .andAppend("params.date", date.format(dateFormat))
            .andAppend("params.previouslyCheckedTimes", previouslyCheckedTimes)

        val lastRetry = !canCheckAgain(previouslyCheckedTimes)
        markers.andAppend("lastRetry", lastRetry)
        val initialDate = date.atStartOfDay(brazilTimeZone)

        val bankAccount = method as InternalBankAccount
        markers.andAppend("params.fullAccountNumber", bankAccount.buildFullAccountNumber())
            .andAppend("params.document", bankAccount.document)

        if (bankAccount.bankNo != arbiBankNo) {
            return bankAccount.buildArbiAccountReportSummary()
        }

        val wallet = walletRepository.findWallets(
            accountId = accountId,
            memberStatus = MemberStatus.ACTIVE,
        ).singleOrNull { it.paymentMethodId == id }

        if (wallet == null) {
            logger.warn(
                markers.andAppend("status", "Conta já encerrada"),
                logName,
            )
            return bankAccount.buildArbiAccountReportSummary()
        }
        markers.andAppend("walletId", wallet.id.value)

        val currentDayStatement = bankAccount.getStatement(
            initialDate = initialDate.toLocalDate(),
            endDate = date.plusDays(endDatePlusDays),
            markers = markers,
        ).filter { it.lastUpdate != null && it.lastUpdate!!.toLocalDate() == date }
        markers.andAppend("currentDayStatement", currentDayStatement.map { it.toLogAttribute() })

        // CREDITS
        val currentDayCredits = customerAccountCreditRepository.findByCreditDate(
            accountPaymentMethodId = id,
            startDate = initialDate,
            endDate = initialDate.plusDays(1),
        )
        markers.andAppend("credit.currentDay", currentDayCredits.map { it.toLogAttribute() })

        val debitsConciliation = checkDebits(
            wallet = wallet,
            date = date,
            cutOffTime = cutOffTime,
            currentDayStatement = currentDayStatement,
            currentDayCredits = currentDayCredits,
            markers = markers,
        )

        val creditsConciliation = checkCredits(
            cutOffTime = cutOffTime,
            currentDayStatement = currentDayStatement,
            currentDayCredits = currentDayCredits,
            markers = markers,
        )

        val summary = bankAccount.buildArbiAccountReportSummary(
            debitsConciliation = debitsConciliation,
            creditsConciliation = creditsConciliation,
        )
        markers.andAppend("summary", summary.toLogAttribute())

        if (summary.divergent) {
            markers.andAppend("status", "Conta divergente")
            if (lastRetry) {
                logger.error(markers, logName)
            } else {
                logger.warn(markers, logName)
            }
        } else {
            markers.andAppend("status", "Conta Ok")
            logger.info(markers, logName)
        }
        return summary
    }

    private fun AccountPaymentMethod.checkCredits(
        cutOffTime: Int,
        currentDayStatement: List<BankStatementItem>,
        currentDayCredits: List<CustomerAccountCredit>,
        markers: LogstashMarker,
    ): ArbiAccountConciliation {
        // CREDITS
        val currentWindowStatementItems = currentDayStatement.filter { (it.isDevolucaoTEF() || it.isDevolucaoTED()) && it.lastUpdate!!.hour < cutOffTime }
        markers.andAppend("credit.statement.items", currentWindowStatementItems.map { it.toLogAttribute() })

        val currentWindowStatementItemsAmount = currentWindowStatementItems.sumOf { it.amount }
        markers.andAppend("credit.statement.amount", currentWindowStatementItemsAmount)

        // REFUNDS and BONUS
        val currentWindowRefundsAndBonuses = currentDayCredits.filter {
            it.createdAt.hour < cutOffTime && it.details.reason.appearsAsDevolucaoTEForTEDInStatement()
        }.toMutableList()
        markers.andAppend("credit.refundsAndBonuses.items", currentWindowRefundsAndBonuses.map { it.toLogAttribute() })

        val currentWindowRefundsAndBonusesAmount = currentWindowRefundsAndBonuses.sumOf { it.amountPaidWithBalance() }
        markers.andAppend("credit.refundsAndBonuses.amount", currentWindowRefundsAndBonusesAmount)

        val conciliation = ArbiAccountConciliation(
            currentAmount = currentWindowStatementItemsAmount,
            expectedAmount = currentWindowRefundsAndBonusesAmount,
        )

        if (conciliation.divergent) {
            var toleranceAmount = 0L

            val toleranceWindowRefundsAndBonuses = currentDayCredits.filter {
                it.createdAt.hour == cutOffTime && it.createdAt.minute < 5 && it.details.reason.appearsAsDevolucaoTEForTEDInStatement()
            }.toMutableList()
            markers.andAppend("credit.refundsAndBonuses.toleranceItems", toleranceWindowRefundsAndBonuses.map { it.toLogAttribute() })

            val divergentStatementItemsWithoutTransaction = currentWindowStatementItems.filter { statementItem ->
                val matchingRefundAmount = currentWindowRefundsAndBonuses.removeMatchingCredit(statementItem)

                matchingRefundAmount == null
            }.filter { statementItem ->
                val matchingRefundAmount = toleranceWindowRefundsAndBonuses.removeMatchingCredit(statementItem)

                matchingRefundAmount?.let {
                    toleranceAmount += it
                }

                matchingRefundAmount == null
            }
            markers.andAppend("credit.divergence.refundsAndBonusesWithoutStatementItem", currentWindowRefundsAndBonuses.map { it.toLogAttribute() })
                .andAppend("credit.divergence.statementItemsWithoutRefundsAndBonuses", divergentStatementItemsWithoutTransaction.map { it.toLogAttribute() })

            return ArbiAccountConciliation(
                currentAmount = currentWindowStatementItemsAmount,
                expectedAmount = currentWindowRefundsAndBonusesAmount + toleranceAmount,
            )
        }

        return conciliation
    }

    private fun checkDebits(
        wallet: Wallet,
        date: LocalDate,
        cutOffTime: Int,
        currentDayStatement: List<BankStatementItem>,
        currentDayCredits: List<CustomerAccountCredit>,
        markers: LogstashMarker,
    ): ArbiAccountConciliation {
        // DEBITS
        val currentDayStatementItems = currentDayStatement.filter { it.flow == BankStatementItemFlow.DEBIT }

        val statementItems = currentDayStatementItems.filter { it.lastUpdate!!.hour < cutOffTime }
        markers.andAppend("debit.statement.items", statementItems.map { it.toLogAttribute() })

        val statementItemsAmount = statementItems.sumOf { it.amount }
        markers.andAppend("debit.statement.amount", statementItemsAmount)

        // BILLS PAID
        val currentDayBillsPaid = billRepository.findByWalletAndStatus(
            walletId = wallet.id,
            status = BillStatus.PAID,
        ).filter { it.source !is ActionSource.OpenFinance && it.paidDate!!.toLocalDate() == date }

        val billsPaid = currentDayBillsPaid.filter { it.paidDate!!.hour < cutOffTime }.toMutableList()
        markers.andAppend("debit.billPaid.items", billsPaid.map { it.toLogAttribute() })

        val billsPaidWithBalanceAmount = billsPaid.sumOf { it.amountPaidWithBalance() }
        markers.andAppend("debit.billPaid.amountPaidWithBalance", billsPaidWithBalanceAmount)

        // BILLS PAID TODAY AND REFUNDED
        val billsPaidTodayAndRefunded = currentDayCredits.filter {
            it.createdAt.hour < cutOffTime
        }.map {
            it.details
        }.filterIsInstance<RefundedBillCustomerAccountCreditDetails>().filter {
            it.originalPaidDate.toLocalDate() == date
        }.toMutableList()
        markers.andAppend("debit.billPaidTodayAndRefunded.items", billsPaidTodayAndRefunded.map { it.toLogAttribute() })

        val billsPaidTodayWithBalanceAndRefundedAmount = billsPaidTodayAndRefunded.sumOf { it.amountPaidWithBalance }
        markers.andAppend("debit.billPaidTodayAndRefunded.amountPaidWithBalance", billsPaidTodayWithBalanceAndRefundedAmount)

        val conciliation = ArbiAccountConciliation(
            currentAmount = statementItemsAmount,
            expectedAmount = billsPaidWithBalanceAmount + billsPaidTodayWithBalanceAndRefundedAmount,
        )

        if (conciliation.divergent) {
            var toleranceAmount = 0L

            val toleranceWindowBillsPaid = currentDayBillsPaid.filter { it.paidDate!!.hour == cutOffTime && it.paidDate.minute < 5 }.toMutableList()
            markers.andAppend("debit.billPaid.toleranceItems", toleranceWindowBillsPaid.map { it.toLogAttribute() })

            val toleranceWindowBillsPaidTodayAndRefunded = currentDayCredits.filter {
                it.createdAt.hour == cutOffTime && it.createdAt.minute < 5
            }.map {
                it.details
            }.filterIsInstance<RefundedBillCustomerAccountCreditDetails>().filter {
                it.originalPaidDate.toLocalDate() == date
            }.toMutableList()
            markers.andAppend("debit.billPaidTodayAndRefunded.toleranceItems", toleranceWindowBillsPaidTodayAndRefunded.map { it.toLogAttribute() })

            val divergentStatementItemsWithoutBillPaid = statementItems.filter { statementItem ->
                val matchingPaymentAmount = billsPaid.removeMatchingBill(statementItem)
                    ?: billsPaidTodayAndRefunded.removeMatchingRefund(statementItem)

                matchingPaymentAmount == null
            }.filter { statementItem ->
                val matchingPaymentAmount = toleranceWindowBillsPaid.removeMatchingBill(statementItem)
                    ?: toleranceWindowBillsPaidTodayAndRefunded.removeMatchingRefund(statementItem)

                matchingPaymentAmount?.let {
                    toleranceAmount += it
                }

                matchingPaymentAmount == null
            }
            markers.andAppend("debit.divergence.billsPaidWithoutStatementItem", billsPaid.map { it.toLogAttribute() })
                .andAppend("debit.divergence.billsPaidTodayAndRefundedWithoutStatementItem", billsPaidTodayAndRefunded.map { it.toLogAttribute() })
                .andAppend("debit.divergence.statementItemsWithoutBillPaid", divergentStatementItemsWithoutBillPaid.map { it.toLogAttribute() })

            return ArbiAccountConciliation(
                currentAmount = statementItemsAmount,
                expectedAmount = billsPaidWithBalanceAmount + billsPaidTodayWithBalanceAndRefundedAmount + toleranceAmount,
            )
        }

        return conciliation
    }

    private fun CustomerAccountCreditReason.appearsAsDevolucaoTEForTEDInStatement() = when (this) {
        CustomerAccountCreditReason.TRANSACTION_ROLLBACK -> false // parece que esse caso não cai no filtro (it.isDevolucaoTEF() || it.isDevolucaoTED())
        CustomerAccountCreditReason.REFUNDED_BILL -> true
        CustomerAccountCreditReason.REFUNDED_SUBSCRIPTION -> true
        CustomerAccountCreditReason.BONUS -> true
    }

    private fun MutableList<BillView>.removeMatchingBill(statementItem: BankStatementItem) = firstOrNull {
        it.amountPaidWithBalance() == statementItem.amount
    }.also {
        remove(it)
    }?.amountPaidWithBalance()

    private fun MutableList<CustomerAccountCredit>.removeMatchingCredit(statementItem: BankStatementItem) = firstOrNull {
        it.amountPaidWithBalance() == statementItem.amount
    }.also {
        remove(it)
    }?.amountPaidWithBalance()

    private fun MutableList<RefundedBillCustomerAccountCreditDetails>.removeMatchingRefund(statementItem: BankStatementItem) = firstOrNull {
        it.amountPaidWithBalance == statementItem.amount
    }.also {
        remove(it)
    }?.amountPaidWithBalance

    private fun InternalBankAccount.getStatement(initialDate: LocalDate, endDate: LocalDate, markers: LogstashMarker) = try {
        accountStatementAdapter
            .getStatement(
                accountNo = buildFullAccountNumber(),
                document = Document(value = document),
                initialDate = initialDate,
                endDate = endDate,
            ).items
    } catch (e: ArbiAccountMissingPermissionsException) {
        markers.andAppend("statementError", "Conta sem permissão. Provavelmente desativada no Arbi por falta de uso.")
        emptyList()
    } catch (e: Exception) {
        markers.andAppend("statementError", "Não foi possível consultar o extrato: ${e.message.orEmpty()}")
        emptyList()
    }

    // FIXME counterpartAccountNo long
    private fun BankStatementItem.isDevolucaoTEF() =
        flow == BankStatementItemFlow.CREDIT &&
            type == BankStatementItemType.TRANSFERENCIA_CC &&
            (
                counterpartAccountNo?.toLong() == arbiSettlementAccountNo.toLong() ||
                    arbiSettlementAccountNo.contains(documentNumber) // FIXME counterpartAccountNo para crédito de TEF não está vindo corretamente. O documentNumber está sendo comparado por isso
                )

    private fun BankStatementItem.isDevolucaoTED() = flow == BankStatementItemFlow.CREDIT && type == BankStatementItemType.DEVOLUCAO_TED

    private fun ArbiAccountReportSummary.toLogAttribute() = mapOf(
        "accountId" to accountId.value,
        "fullAccountNumber" to accountNumber.fullAccountNumber,
        "date" to date.format(dateFormat),
        "cutOffTime" to cutOffTime,
        "debitsConciliation" to debitsConciliation,
        "refundsConciliation" to refundsConciliation,
        "divergent" to divergent,
    )

    private fun CustomerAccountCredit.amountPaidWithBalance() = when (this.details) {
        is BonusCustomerAccountCreditDetails -> amount
        is TransactionRollbackCustomerAccountCreditDetails -> amount
        is RefundedSubscriptionCustomerAccountCreditDetails -> amount
        is RefundedBillCustomerAccountCreditDetails -> details.amountPaidWithBalance
    }

    private fun CustomerAccountCredit.toLogAttribute() = mapOf(
        "amount" to amount,
        "fromInternalBankAccount" to fromInternalBankAccount,
        "createdAt" to createdAt.format(dateTimeFormat),
        "details" to details.toLogAttribute(),
    )

    private fun CustomerAccountCreditDetails.toLogAttribute() = when (this) {
        is BonusCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "description" to description,
        )
        is TransactionRollbackCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "transactionId" to transactionId.value,
        )
        is RefundedSubscriptionCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "billId" to billId.value,
        )
        is RefundedBillCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "billId" to billId.value,
            "transactionId" to transactionId.value,
            "amountPaidWithBalance" to amountPaidWithBalance,
            "amountPaidWithCreditCard" to amountPaidWithCreditCard,
            "type" to type,
            "originalPaidDate" to originalPaidDate.format(dateTimeFormat),
        )
    }

    private fun BankStatementItem.toLogAttribute() = mapOf(
        "amount" to amount,
        "date" to date.format(dateFormat),
        "description" to description,
        "operationNumber" to operationNumber,
        "ref" to ref,
        "lastUpdate" to lastUpdate?.format(dateTimeFormat),
        "documentNumber" to documentNumber,
        "counterpartAccountNo" to counterpartAccountNo,
        "flow" to flow,
    )

    private fun BillView.toLogAttribute() = mapOf(
        "amount" to amountPaid,
        "amountPaidWithBalance" to amountPaidWithBalance(),
        "amountPaidWithCreditCard" to amountPaidWithCreditCard(),
        "billId" to billId.value,
        "paidDate" to paidDate?.format(dateTimeFormat),
        "type" to billType,
    )

    fun sendEmailReport(
        date: LocalDate,
        cutOffTime: Int,
        settlementAccountReport: SettlementAccountReport,
        cashInAccountReport: SettlementAccountReport,
        allBoletosCreditCardRefunded: Long,
        recipient: String,
    ) {
        val title =
            if (cutOffTime != 24) {
                "$subject - ${date.format(dateFormat)} até as ${cutOffTime}hs"
            } else {
                "$subject - ${date.format(dateFormat)} (dia completo)"
            }

        val content =
            buildString {
                appendLine("---   ---   ---   PAGAMENTOS UTILIZANDO SALDO   ---   ---   ---")
                appendLine("Total Créditos Conta Liquidação Arbi: ${settlementAccountReport.totalAmountArbi.toAmountFormat()}")
                appendLine("Total boletos liquidados com saldo do usuário: ${settlementAccountReport.totalAmountFriday.toAmountFormat()}")
                appendLine("Diferença: ${(settlementAccountReport.totalAmountArbi - settlementAccountReport.totalAmountFriday).toAmountFormat()}")
                appendLine("")
                appendLine("---   ---   ---   PAGAMENTOS UTILIZANDO CARTÃO   ---   ---   ---")
                appendLine("Total transferido da conta Cash-in para Celcoin: ${cashInAccountReport.totalAmountArbi.toAmountFormat()}")
                appendLine("Total boletos liquidados com cartão de crédito: ${cashInAccountReport.totalAmountFriday.toAmountFormat()}")
                appendLine("Diferença: ${(cashInAccountReport.totalAmountArbi - cashInAccountReport.totalAmountFriday).toAmountFormat()}")
                appendLine("")
                appendLine("---   ---   ---   ESTORNOS DE BOLETOS PAGOS COM CARTÃO   ---   ---   ---")
                appendLine("Total boletos estornados com cartão de crédito: ${allBoletosCreditCardRefunded.toAmountFormat()}")
                appendLine("Total transferido da conta liquidação Arbi para a conta Cashin: ${cashInAccountReport.totalRefundedToCashinAccount.toAmountFormat()}")
                appendLine("Diferença: ${(allBoletosCreditCardRefunded - cashInAccountReport.totalRefundedToCashinAccount).toAmountFormat()}")
            }

        emailSender.sendRawEmail(from, title, content, recipient)
    }

    fun generateSettlementAccountReport(
        date: LocalDate,
        cutOffTime: Int,
    ): Triple<SettlementAccountReport, SettlementAccountReport, Long> {
        val accounts = accountRepository.findAllAccountsActivatedSince(LocalDate.EPOCH)

        val allWallets =
            accounts
                .map { account ->
                    walletRepository
                        .findWallets(account.accountId, MemberStatus.ACTIVE)
                        .filter { it.founder.accountId == account.accountId }
                }.flatten()

        val allBoletosPaid =
            allWallets
                .map { wallet ->
                    billRepository
                        .findByWalletAndStatus(walletId = wallet.id, status = BillStatus.PAID)
                        .filter { it.billType.isBoleto() && it.paidDate!!.toLocalDate() == date && it.paidDate.hour < cutOffTime }
                }.flatten()

        val allBoletosPaidAmountBalanceTotal =
            allBoletosPaid.sumOf {
                it.amountPaidWithBalance()
            }

        val allBoletosPaidAmountCreditCardTotal =
            allBoletosPaid.sumOf {
                it.amountPaidWithCreditCard()
            }

        val allBoletosCreditCardRefunded =
            allWallets
                .flatMap { wallet ->
                    getCurrentWindowBillsRefunded(
                        accountPaymentMethodId = wallet.paymentMethodId,
                        initialDate = date.atStartOfDay(brazilTimeZone),
                        cutOffTime = cutOffTime,
                    )
                }.sumOf { it.amountPaidWithCreditCard }

        val balanceStatementItems =
            arbiAdapter
                .getStatement(
                    accountNumber = AccountNumber(arbiSettlementAccountNo),
                    document = arbiSettlementAccountDocument,
                    initialDate = date.atStartOfDay(brazilTimeZone),
                    endDate = date.plusDays(endDatePlusDays).atStartOfDay(brazilTimeZone),
                ).items
                .filter { it.lastUpdate != null && it.lastUpdate!!.toLocalDate() == date && it.lastUpdate!!.hour < cutOffTime }

        val credits =
            balanceStatementItems
                .filter { it.flow == BankStatementItemFlow.CREDIT }
                .sumOf { it.amount }

        val returnedToUser =
            balanceStatementItems
                .filter { it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.TRANSFERENCIA_CC && it.counterpartDocument != arbiSettlementAccountDocument }
                .sumOf { it.amount }

        val returnedToFriday =
            balanceStatementItems
                .filter { it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.PIX && it.counterpartDocument == arbiSettlementAccountDocument }
                .sumOf { it.amount }

        val settlementAccountReport =
            SettlementAccountReport(
                totalAmountArbi = credits - returnedToUser,
                totalAmountFriday = allBoletosPaidAmountBalanceTotal,
                totalRefundedToCashinAccount = returnedToFriday,
            )

        val creditCardCashInAccountStatementItems =
            if (arbiCashInAccountNo != "0") {
                arbiAdapter
                    .getStatement(
                        accountNumber = AccountNumber(arbiCashInAccountNo),
                        document = arbiSettlementAccountDocument,
                        initialDate = date.atStartOfDay(brazilTimeZone),
                        endDate = date.plusDays(endDatePlusDays).atStartOfDay(brazilTimeZone),
                    ).items
                    .filter { it.lastUpdate != null && it.lastUpdate!!.toLocalDate() == date && it.lastUpdate!!.hour < cutOffTime }
            } else {
                emptyList()
            }

        val pixToFridayCNPJ =
            creditCardCashInAccountStatementItems
                .filter { it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.PIX && it.counterpartDocument == arbiSettlementAccountDocument }
                .sumOf { it.amount }

        val depositsFromSettlement =
            creditCardCashInAccountStatementItems
                .filter { it.flow == BankStatementItemFlow.CREDIT && it.type == BankStatementItemType.PIX && it.counterpartDocument == arbiSettlementAccountDocument }
                .sumOf { it.amount }

        creditCardCashInAccountStatementItems
            .filter {
                it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.PIX && it.counterpartDocument != arbiSettlementAccountDocument
            }.also { pixToOthersCNPJs ->
                if (pixToOthersCNPJs.isNotEmpty()) {
                    logger.error(
                        Markers
                            .append("ACTION", "VERIFY")
                            .andAppend("context", "Pix saindo da conta Cashin para uma conta que não é Friday detectado")
                            .andAppend(
                                "bankStatementItems",
                                pixToOthersCNPJs,
                            ),
                        "GenerateSettlementAccountReport",
                    )
                }
            }

        val cashInAccountReport =
            SettlementAccountReport(
                totalAmountArbi = pixToFridayCNPJ,
                totalAmountFriday = allBoletosPaidAmountCreditCardTotal,
                totalRefundedToCashinAccount = depositsFromSettlement,
            )

        return Triple(settlementAccountReport, cashInAccountReport, allBoletosCreditCardRefunded)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BankAccountReconciliationReportService::class.java)
    }
}

data class ArbiAccountReportSummary(
    val accountId: AccountId,
    val accountNumber: AccountNumber,
    val date: LocalDate,
    val cutOffTime: Int,
    val debitsConciliation: ArbiAccountConciliation,
    val refundsConciliation: ArbiAccountConciliation,
) {
    val divergent: Boolean = debitsConciliation.divergent || refundsConciliation.divergent
}

data class ArbiAccountConciliation(
    val currentAmount: Long,
    val expectedAmount: Long,
) {
    val divergent: Boolean = currentAmount != expectedAmount
    val missingAmount: Long = currentAmount - expectedAmount

    companion object {
        val ZERO = ArbiAccountConciliation(0, 0)
    }
}

data class SettlementAccountReport(
    val totalAmountArbi: Long,
    val totalAmountFriday: Long,
    val totalRefundedToCashinAccount: Long,
) {
    fun hasDivergence() = totalAmountArbi != totalAmountFriday
}

data class AccountReconciliationReportQueueMessageTO(
    val date: String,
    val cutOffTime: Int,
    val accountId: String,
    val accountPaymentMethodId: String,
    val retry: Int,
)