package ai.friday.billpayment.app.statement

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.integrations.Statement
import ai.friday.billpayment.app.integrations.StatementItemConverter
import ai.friday.billpayment.app.integrations.StatementTemplateCompiler
import ai.friday.billpayment.app.integrations.StatementTemplateForm
import ai.friday.billpayment.app.stripAccents
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import com.fasterxml.jackson.dataformat.csv.CsvMapper
import java.io.StringWriter
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@FridayMePoupe
class FridayStatementItemConverter(private val statementTemplateCompiler: StatementTemplateCompiler) :
    StatementItemConverter {

    override fun convertToCsv(items: List<StatementItem>): String {
        val csvSchema = CsvMapper().schemaFor(StatementCSV::class.java).withHeader()
        val csvData = StringWriter()
        val sequenceWriter = CsvMapper().writer(csvSchema).writeValues(csvData)
        if (items.isEmpty()) {
            sequenceWriter.write("")
        }
        items.forEach {
            sequenceWriter.write(it.toStatementCSV())
        }
        return csvData.toString()
    }

    override fun convertToPdf(
        statement: Statement,
    ): ByteArray {
        val html = statementTemplateCompiler.buildStatementHtml(
            StatementTemplateForm(
                name = statement.name,
                walletName = statement.walletName,
                document = statement.bankAccount.document,
                bankAccount = statement.bankAccount,
                startDate = statement.startDate,
                endDate = statement.endDate,
                created = statement.created,
                statementItems = statement.items,
                initialBalance = statement.initialBalance,
                finalBalance = statement.finalBalance,
                initialBalanceDate = statement.initialBalanceDate,
                finalBalanceDate = statement.finalBalanceDate,
            ),
        )

        return statementTemplateCompiler.toPDF(html)
    }

    override fun convertToOfx(statement: Statement): ByteArray = with(statement) {
        val builder = StringBuilder()

        // OFX Header
        builder.append("OFXHEADER=100\n")
            .append("DATA=OFXSGML\n")
            .append("VERSION=102\n")
            .append("SECURITY=NONE\n")
            .append("ENCODING=USASCII\n")
            .append("CHARSET=1252\n")
            .append("COMPRESSION=NONE\n")
            .append("OLDFILEUID=NONE\n")
            .append("NEWFILEUID=NONE\n\n")

        // OFX Body
        builder.append("<OFX>\n")

        // Bank Statement Response
        builder.append("<BANKMSGSRSV1>\n")
            .append("<STMTTRNRS>\n")
            .append("<TRNUID>1</TRNUID>\n")
            .append("<STATUS>\n")
            .append("<CODE>0</CODE>\n")
            .append("<SEVERITY>INFO</SEVERITY>\n")
            .append("</STATUS>\n")

        // Statement Response
        builder.append("<STMTRS>\n")

        // Currency
        builder.append("<CURDEF>BRL</CURDEF>\n")

        // Bank Account Info
        with(bankAccount) {
            builder.append("<BANKACCTFROM>\n")
                .append("<BANKID>$bankNo</BANKID>\n")
                .append("<ACCTID>${accountNo}$accountDv</ACCTID>\n")
                .append("<ACCTTYPE>${accountType.toOfxBankAccountType()}</ACCTTYPE>\n")
                .append("</BANKACCTFROM>\n")
        }

        // Initial Balance
        builder.append("<LEDGERBAL>\n")
            .append("<BALAMT>${initialBalance.amount.toOfxAmount()}</BALAMT>\n")
            .append("<DTASOF>${initialBalanceDate.toOfxDate()}</DTASOF>\n")
            .append("</LEDGERBAL>\n")

        // Statement Period
        builder.append("<BANKTRANLIST>\n")
            .append("<DTSTART>${startDate.toOfxDate()}</DTSTART>\n")
            .append("<DTEND>${endDate.toOfxDate()}</DTEND>\n")

        // Transactions
        items.forEach { item ->
            builder.append("<STMTTRN>\n")
                .append("<TRNTYPE>${item.flow.toOfxFlow()}</TRNTYPE>\n")
                .append("<DTPOSTED>${item.postedAt.toOfxDateTime()}</DTPOSTED>\n")
                .append("<TRNAMT>${item.amount.toOfxAmount(item.flow)}</TRNAMT>\n")
                .append("<FITID>${item.id}</FITID>\n")
                .append("<MEMO>${item.description.toOfxSafeString()}</MEMO>\n")
                .append("<NAME>${item.counterPartName.toOfxSafeString()}</NAME>\n")
                .append("</STMTTRN>\n")
        }

        builder.append("</BANKTRANLIST>\n")

        // Ledger Balance
        builder.append("<LEDGERBAL>\n")
            .append("<BALAMT>${finalBalance.amount.toOfxAmount()}</BALAMT>\n")
            .append("<DTASOF>${finalBalanceDate.toOfxDate()}</DTASOF>\n")
            .append("</LEDGERBAL>\n")

        builder.append("</STMTRS>\n")
            .append("</STMTTRNRS>\n")
            .append("</BANKMSGSRSV1>\n")
            .append("</OFX>")

        return builder.toString().toByteArray(Charsets.US_ASCII)
    }

    private fun StatementItem.toStatementCSV() = StatementCSV(
        valor = formatAmount(this),
        nome = this.counterPartName,
        data = this.postedAt.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")),
        descricao = this.description,
        saldo = formatBalance(this),
        categoria = this.category ?: "",
    )

    private fun formatAmount(item: StatementItem): String {
        val amount = (item.amount.toFloat() / 100f) * (if (item.flow == BankStatementItemFlow.DEBIT) -1 else 1) // Debitos devem ir com valor negativo
        return amount.toString()
    }

    private fun formatBalance(item: StatementItem): String {
        val amount = (item.balance.amount.toFloat() / 100f)
        return amount.toString()
    }

    private fun Long.toOfxAmount(flow: BankStatementItemFlow? = BankStatementItemFlow.CREDIT): String {
        return String.format("%.2f", (this.toFloat() / 100f) * if (flow == BankStatementItemFlow.DEBIT) -1 else 1)
    }

    private fun BankStatementItemFlow.toOfxFlow(): String {
        return when (this) {
            BankStatementItemFlow.CREDIT -> "CREDIT"
            BankStatementItemFlow.DEBIT -> "DEBIT"
        }
    }

    private fun AccountType.toOfxBankAccountType(): String {
        return when (this) {
            AccountType.CHECKING, AccountType.SALARY, AccountType.PAYMENT -> "CHECKING"
            AccountType.SAVINGS -> "SAVINGS"
        }
    }

    private fun LocalDate.toOfxDate(): String {
        return this.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
    }

    private fun ZonedDateTime.toOfxDateTime(): String {
        return this.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
    }

    private fun String.toOfxSafeString(): String {
        return this.stripAccents().replace(Regex("[<>]"), "")
    }
}

@JsonPropertyOrder(value = ["data", "nome", "descricao", "valor", "saldo", "categoria"])
data class StatementCSV(
    val valor: String,
    val nome: String,
    val data: String,
    val descricao: String,
    val saldo: String,
    val categoria: String,
)