<!DOCTYPE html>
<html lang="pt-BR">
    <head>
        <title>GigU Conta - Extrato</title>
        <meta charset="utf-8"/>
        <meta name="color-scheme" content="only"/>
        <meta name="supported-color-schemes" content="only"/>
    <style type="text/css">body{background-color:#fff;color:#444a4b;font-family:"Mulish",sans-serif;font-size:16px;-webkit-font-smoothing:antialiased;letter-spacing:.42px}.header-left{position:running(header-left);font-family:Mulish;padding-top:50px;display:block}.header-left .image{box-sizing:border-box;vertical-align:middle;height:32px}.header-right{position:running(header-right);font-family:Mulish;text-align:right;font-style:normal;color:#94a3b8;padding-top:50px;display:block}.header-right .emitidoEm{font-size:12px;font-weight:500;line-height:16px}.header-right::after{content:"Página " counter(page) " de " counter(pages);font-size:12px;font-weight:500;line-height:16px;color:#94a3b8;font-family:Mulish}.main{background-color:#fff}.main .title{border-radius:16px 16px 16px 16px;background:#fff4ef}.main .title .subtitle{margin-top:16px;font-family:Mulish;color:#565B61;font-size:12px;font-weight:600;line-height:16px}.main .title .subtitle .name{text-align:right}.main .title .subtitle .document{text-align:right}.main .content{margin-top:16px;border-radius:16px 16px 16px 16px;border-right:1px solid #f1f5f9;border-bottom:1px solid #f1f5f9;border-left:1px solid #f1f5f9;border-top:1px solid #f1f5f9}.main .content .subheader{padding:16px 24px}.main .content .subheader .subtitle{color:#565B61;font-family:Mulish;font-size:16px;font-style:normal;font-weight:700;line-height:24px;padding:16px 0 0 24px}.main .content .subheader .subheader-info{color:#64748b;font-family:Mulish;font-size:12px;font-style:normal;font-weight:500;line-height:16px}.main .content .subheader .subheader-info .left{text-align:left;padding-top:0;padding-left:24px;font-weight:500}.main .content .subheader .subheader-info .right{text-align:right;padding-top:0;padding-right:24px;font-weight:500}.main table{-fs-table-paginate:paginate;width:100%;border-collapse:collapse}.main table h3{margin-bottom:18px}.main table tr{margin:0}.main table tr+tr{border-top:0}.main table:first-child{margin-top:0}.main .data-entry+.main .data-entry{margin-top:24px}.main .data-entry .table-header{border-bottom:1px solid #f1f5f9;margin:0;padding:24px 24px 12px 24px}.main .data-entry .table-header th{color:#565B61;font-family:Mulish;font-size:14px;font-style:normal;font-weight:700 !important;line-height:20px;text-align:left;padding:24px 0 12px 0}.main .data-entry .table-header .date{width:80px;padding-left:24px}.main .data-entry .table-header .name{width:200px}.main .data-entry .table-header .value,.main .data-entry .table-header .balance{width:80px;text-align:right;padding-right:24px}.main .data-entry .data-row{page-break-inside:avoid;margin:0;line-height:1}.main .data-entry .data-row td{color:#565B61;font-family:Mulish;font-size:14px;font-style:normal;font-weight:500;line-height:20px;min-height:48px}.main .data-entry .data-row td .debit{color:#cd0023}.main .data-entry .data-row td .amount{color:#38ac61}.main .data-entry .data-row .data-date{padding-left:24px}.main .data-entry .data-row .data-name .name{display:block}.main .data-entry .data-row .data-name .description{font-size:12px;color:#64748b}.main .data-entry .data-row .data-balance,.main .data-entry .data-row .data-value{text-align:right;padding-right:24px;font-weight:700}ul{list-style:none;margin:0;padding:0;list-style:decimal;list-style-position:inside}p+a,p+div{margin-top:16px}a{color:#1e273e}.title{display:flex;padding:24px;flex-direction:column;justify-content:flex-end;align-items:flex-start;align-self:stretch}.title span{align-self:stretch;color:#FF5900;font-family:Mulish;font-size:20px;font-weight:700;line-height:20px}h1,h2,h3,h4,li,p{margin:0}@page{size:A4 portrait;margin:120px 48px 57px 48px;@top-left{content:element(header-left);padding-left:24px;display:block}@top-center{display:block}@top-right{content:element(header-right);padding-right:24px;display:block}@bottom-center{font-size:14px;text-align:center;color:#7a869a}}
</style></head>
    <body style="background-color: #fff; color: #444a4b; font-family: 'Mulish',sans-serif; font-size: 16px; -webkit-font-smoothing: antialiased; letter-spacing: .42px;">

        <div class="header-left" style="position: running(header-left); font-family: Mulish; padding-top: 50px; display: block;">
            <img class="image" src="https://notification-templates-cdn.via1.app/static/gigu-conta-logo.png" alt="GigU" style="box-sizing: border-box; vertical-align: middle; width: 150px; height: auto;"/>
        </div>
        <div class="header-right" style="position: running(header-right); font-family: Mulish; text-align: right; font-style: normal; color: #94a3b8; padding-top: 50px; display: block;">
            <p class="emitidoEm" style="margin: 0; font-size: 12px; font-weight: 500; line-height: 16px;">Emitido em {{created}}</p>
        </div>

        <div class="main" style="background-color: #fff;">
            <div class="title" style="display: flex; padding: 24px; flex-direction: column; justify-content: flex-end; align-items: flex-start; align-self: stretch; border-radius: 16px 16px 16px 16px; background: #fff4ef;">
                <span style="align-self: stretch; color: #FF5900; font-family: Mulish; font-size: 20px; font-weight: 700; line-height: 20px;">Extrato da carteira {{walletName}}</span>
                <table class="subtitle" style="-fs-table-paginate: paginate; width: 100%; border-collapse: collapse; margin-top: 16px; font-family: Mulish; color: #565B61; font-size: 12px; font-weight: 600; line-height: 16px;" width="100%">
                    <tr style="margin: 0;">
                        <td>
                            <p class="bank" style="margin: 0;">{{bankName}} - {{bankNo}}</p>
                        </td>
                        <td>
                            <p class="name" style="margin: 0; text-align: right;">{{name}}</p>
                        </td>
                    </tr>
                    <tr style="margin: 0; border-top: 0;">
                        <td>
                            <p class="routing" style="margin: 0;">Ag. {{routingNo}} - Conta {{accountNo}}</p>
                        </td>
                        <td>
                            <p class="document" style="margin: 0; text-align: right;">{{document}}</p>
                        </td>
                    </tr>
            </table></div>

            <div class="content" style="margin-top: 16px; border-radius: 16px 16px 16px 16px; border-right: 1px solid #f1f5f9; border-bottom: 1px solid #f1f5f9; border-left: 1px solid #f1f5f9; border-top: 1px solid #f1f5f9;">
                <table class="subheader" style="-fs-table-paginate: paginate; width: 100%; border-collapse: collapse; margin-top: 0; padding: 16px 24px;" width="100%">
                    <tr style="margin: 0;">
                        <td class="subtitle" style="color: #565B61; font-family: Mulish; font-size: 16px; font-style: normal; font-weight: 700; line-height: 24px; padding: 16px 0 0 24px;">Lançamentos</td>
                    </tr>
                    <tr class="subheader-info" style="margin: 0; border-top: 0; color: #64748b; font-family: Mulish; font-size: 12px; font-style: normal; font-weight: 500; line-height: 16px;">
                        <td class="left" style="text-align: left; padding-top: 0; padding-left: 24px; font-weight: 500;" align="left">Período de visualização: {{startDate}} a {{endDate}}</td>
                    </tr>
                </table>
                <table class="data-entry" width="100%" style="-fs-table-paginate: paginate; width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr class="table-header" style="border-bottom: 1px solid #f1f5f9; margin: 0; padding: 24px 24px 12px 24px;">
                            <th class="date" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; text-align: left; padding: 24px 0 12px 0; width: 80px; padding-left: 24px; font-weight: 700;" width="80" align="left">Data</th>
                            <th class="name" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; text-align: left; padding: 24px 0 12px 0; width: 200px; font-weight: 700;" width="200" align="left">Título</th>
                            <th class="value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; padding: 24px 0 12px 0; width: 80px; text-align: right; padding-right: 24px; font-weight: 700;" width="80" align="right">Valor (R$)</th>
                            <th class="balance" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; padding: 24px 0 12px 0; width: 80px; text-align: right; padding-right: 24px; font-weight: 700;" width="80" align="right">Saldo (R$)</th>
                        </tr>
                    </thead>
                    <tr class="data-row" style="page-break-inside: avoid; margin: 0; line-height: 1;">
                        <td class="data-date" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px; padding-left: 24px;">{{initialBalanceDate}}</td>
                        <td class="data-name" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px;">Saldo anterior</td>
                        <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span/></td>
                        <td class="data-balance" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right">{{initialBalance}}</td>
                    </tr>
                    {{#each statementItems}}
                    {{#if isEvenEntry}}
                        <tr class="data-row" style="border-top: 0; page-break-inside: avoid; margin: 0; line-height: 1; background: #F1F5F9;">
                            <td class="data-date" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px; padding-left: 24px;">{{date}}</td>
                            <td class="data-name" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px;">
                                <span class="name" style="display: block;">{{name}}</span>
                                <span class="description" style="font-size: 12px; color: #64748b;">{{description}}</span>
                            </td>
                            {{#if isDebit}}
                                <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span class="debit" style="color: #cd0023;">{{value}}</span></td>
                            {{else}}
                                <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span class="amount" style="color: #38ac61;">{{value}}</span></td>
                            {{/if}}
                            <td class="data-balance" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"/>
                        </tr>
                    {{else}}
                        <tr class="data-row" style="border-top: 0; page-break-inside: avoid; margin: 0; line-height: 1;">
                            <td class="data-date" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px; padding-left: 24px;">{{date}}</td>
                            <td class="data-name" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px;">
                                <span class="name" style="display: block;">{{name}}</span>
                                <span class="description" style="font-size: 12px; color: #64748b;">{{description}}</span>
                            </td>
                            {{#if isDebit}}
                                <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span class="debit" style="color: #cd0023;">{{value}}</span></td>
                            {{else}}
                                <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span class="amount" style="color: #38ac61;">{{value}}</span></td>
                            {{/if}}
                            <td class="data-balance" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"/>
                        </tr>
                    {{/if}}
                    {{/each}}
                    {{#if isEvenFinalBalance}}
                    <tr class="data-row" style="border-top: 0; page-break-inside: avoid; margin: 0; line-height: 1; background: #F1F5F9;">
                        <td class="data-date" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px; padding-left: 24px;">{{finalBalanceDate}}</td>
                        <td class="data-name" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px;">Saldo do dia</td>
                        <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span/></td>
                        <td class="data-balance" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right">{{finalBalance}}</td>
                    </tr>
                    {{else}}
                    <tr class="data-row" style="border-top: 0; page-break-inside: avoid; margin: 0; line-height: 1;">
                        <td class="data-date" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px; padding-left: 24px;">{{finalBalanceDate}}</td>
                        <td class="data-name" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; font-weight: 500; line-height: 20px; min-height: 48px;">Saldo do dia</td>
                        <td class="data-value" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right"><span/></td>
                        <td class="data-balance" style="color: #565B61; font-family: Mulish; font-size: 14px; font-style: normal; line-height: 20px; min-height: 48px; text-align: right; padding-right: 24px; font-weight: 700;" align="right">{{finalBalance}}</td>
                    </tr>
                    {{/if}}
                </table>
            </div>
        </div>
    </body>
</html>