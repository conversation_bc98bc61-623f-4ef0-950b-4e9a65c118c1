<!DOCTYPE html>
<html lang="pt-BR" style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">
<head>
    <title>GigU Conta</title>
    <meta charset="utf-8">
    <meta name="color-scheme" content="only">
    <meta name="supported-color-schemes" content="only">
    <style type="text/css">
/* Document
 * ========================================================================== */
    *,
    ::before,
    ::after {
        box-sizing: border-box;
    }

    /**
     * 1. Add text decoration inheritance in all browsers (opinionated).
     * 2. Add vertical alignment inheritance in all browsers (opinionated).
     */
    ::before,
    ::after {
        text-decoration: inherit; /* 1 */
        vertical-align: inherit; /* 2 */
    }

    html {
        cursor: default; /* 1 */
        line-height: 1.5; /* 2 */
        -moz-tab-size: 4; /* 3 */
        tab-size: 4; /* 3 */
        -webkit-tap-highlight-color: transparent; /* 4 */
        -ms-text-size-adjust: 100%; /* 5 */
        -webkit-text-size-adjust: 100%; /* 5 */
        word-break: break-word; /* 6 */
    }

    body {
        margin: 0;
    }

    h1 {
        font-size: 2em;
        margin: 0.67em 0;
    }

    dl dl,
    dl ol,
    dl ul,
    ol dl,
    ul dl {
        margin: 0;
    }

    ol ol,
    ol ul,
    ul ol,
    ul ul {
        margin: 0;
    }

    hr {
        height: 0; /* 1 */
        overflow: visible; /* 2 */
    }

    main {
        display: block;
    }

    nav ol,
    nav ul {
        list-style: none;
        padding: 0;
    }

    pre {
        font-family: monospace, monospace; /* 1 */
        font-size: 1em; /* 2 */
    }

    abbr[title] {
        text-decoration: underline;
        text-decoration: underline dotted;
    }

    b,
    strong {
        font-weight: bolder;
    }

    code,
    kbd,
    samp {
        font-family: monospace, monospace; /* 1 */
        font-size: 1em; /* 2 */
    }

    small {
        font-size: 80%;
    }

    audio,
    canvas,
    iframe, img, svg, video {
        vertical-align: middle;
    }

    iframe {
        border-style: none;
    }

    svg:not([fill]) {
        fill: currentColor;
    }

    svg:not(:root) {
        overflow: hidden;
    }

    table {
        border-collapse: collapse;
    }

    button,
    input,
    select {
        margin: 0;
    }

    button {
        overflow: visible;
        text-transform: none;
    }

    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
        -webkit-appearance: button;
    }

    fieldset {
        border: 1px solid #a0a0a0;
        padding: 0.35em 0.75em 0.625em;
    }

    input {
        overflow: visible;
    }

    legend {
        color: inherit;
        display: table;
        max-width: 100%;
        white-space: normal;
    }

    progress {
        display: inline-block;
        vertical-align: baseline;
    }

    select {
        text-transform: none;
    }

    textarea {
        margin: 0;
        overflow: auto;
        resize: vertical;
    }

    [type="search"] {
        -webkit-appearance: textfield;
        outline-offset: -2px;
    }

    ::-webkit-inner-spin-button,
    ::-webkit-outer-spin-button {
        height: auto;
    }

    ::-webkit-input-placeholder {
        color: inherit;
        opacity: 0.54;
    }

    ::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
    }

    ::-moz-focus-inner {
        border-style: none;
        padding: 0;
    }

    :-moz-focusring {
        outline: 1px dotted ButtonText;
    }

    :-moz-ui-invalid {
        box-shadow: none;
    }

    details {
        display: block;
    }

    dialog {
        background-color: white;
        border: solid;
        color: black;
        display: block;
        height: -moz-fit-content;
        height: -webkit-fit-content;
        height: fit-content;
        left: 0;
        margin: auto;
        padding: 1em;
        position: absolute;
        right: 0;
        width: -moz-fit-content;
        width: -webkit-fit-content;
        width: fit-content;
    }

    dialog:not([open]) {
        display: none;
    }

    summary {
        display: list-item;
    }

    template {
        display: none;
    }

    a,
    area,
    button,
    input,
    label,
    select,
    summary,
    textarea,
    [tabindex] {
        -ms-touch-action: manipulation;
        touch-action: manipulation;
    }

    [aria-busy="true"] {
        cursor: progress;
    }

    [aria-controls] {
        cursor: pointer;
    }

    [aria-disabled="true"],
    [disabled] {
        cursor: not-allowed;
    }

    [aria-hidden="false"][hidden] {
        display: initial;
    }

    [aria-hidden="false"][hidden]:not(:focus) {
        clip: rect(0, 0, 0, 0);
        position: absolute;
    }

    body {
        background-color: #f5f6f7;
        color: #444a4b;
    }

    #base-layout {
        font-size: 16px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        padding: 30px 0;
        background-color: #f5f6f7;
    }

    #base-layout > .main, #base-layout > .footer {
        max-width: 600px;
        margin: 0 auto;
    }

    #base-layout > .main {
        background-color: #fff;
        border-radius: 10px;
    }

    #base-layout > .footer {
        padding: 30px 40px;
        font-size: 13px;
        text-align: center;
        color: #7a869a;
    }

    #base-layout > .footer .links {
        margin-top: 20px;
    }

    #base-layout > .footer .links a + a {
        margin-left: 12px;
    }

    #base-layout > .footer p {
        text-align: center;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    #base-layout h1, #base-layout h2, #base-layout h3, #base-layout h4, #base-layout li, #base-layout p {
        margin: 0;
    }

    #base-layout p {
        text-align: left;
        line-height: 1.5;
        color: #444a4b;
    }

    #base-layout p + p, #base-layout p + a, #base-layout p + div {
        margin-top: 16px;
    }

    #base-layout a {
        color: #444a4b;
    }

    #base-layout .action {
        display: block;
        padding: 20px;
        margin: 30px 0;
        text-align: center;
        font-size: 18px;
        text-decoration: none;
        font-weight: 800;
        border-radius: 10px;
        background-color: #FF5900;
        color: #fff;
        box-shadow: 0 13px 10px -10px rgba(255, 89, 0, .5);
    }

    #message-layout {
        padding: 30px 40px;
    }

    #message-layout > .header {
        margin-bottom: 30px;
    }

    #message-layout > .signature {
        margin-top: 30px;
    }

    #list li {
        margin: 10px 0;
    }
    </style>
</head>

<body style="box-sizing: border-box; margin: 0; background-color: #f5f6f7; color: #444a4b;">
<div id="base-layout" style="box-sizing: border-box; font-size: 16px; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; -webkit-font-smoothing: antialiased; padding: 30px 0; background-color: #f5f6f7;">
    <div class="main" style="box-sizing: border-box; max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 10px;">
        <div id="message-layout" style="box-sizing: border-box; padding: 30px 40px;">
            <div class="header" style="box-sizing: border-box; margin-bottom: 30px;">
                <img src="https://notification-templates-cdn.via1.app/static/gigu-conta-logo.png" alt="GigU Conta" style="box-sizing: border-box; vertical-align: middle; width: 150px; height: auto;">
            </div>

            <div class="content" style="box-sizing: border-box;">
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Olá,</p>
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">
                    <strong style="box-sizing: border-box; font-weight: bolder;">{{founderName}}</strong> está aguardando o seu aceite ao convite para compartilhar a carteira GigU Conta e simplificar a rotina de pagamento de contas de vocês.
                </p>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">
                    Ao aceitar o convite, você incluirá contas e despesas na carteira conjunta, centralizando e <strong style="box-sizing: border-box; font-weight: bolder;">organizando todos os pagamentos e despesas em um só app.</strong>
                </p>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">
                    O jeito mais fácil de pagar contas é com GigU Conta!
                </p>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">Vamos começar?</p>

                <ul id="list" style="box-sizing: border-box;">
                    <li style="box-sizing: border-box; margin: 10px 0;">Tenha sua CNH ou RG física ou em um arquivo digital;</li>
                    <li style="box-sizing: border-box; margin: 10px 0;">Baixe o aplicativo da GigU Conta <a href="https://use.giguconta.app" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">clicando aqui</a>;</li>
                    <li style="box-sizing: border-box; margin: 10px 0;">Faça seu cadastro, aguarde a aprovação e então aceite o convite dentro do App.</li>
                </ul>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Ficou com alguma dúvida? <a href="https://wa.me/5521971492540" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">Clique aqui</a> e fale com a gente</p>
            </div>

            <div class="signature" style="box-sizing: border-box; margin-top: 30px;">
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Um abraço,</p>
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">Equipe GigU Conta</p>
            </div>
        </div>
    </div>

    <div class="footer" style="box-sizing: border-box; max-width: 600px; margin: 0 auto; padding: 30px 40px; font-size: 13px; text-align: center; color: #7a869a;">
        <p style="box-sizing: border-box; margin: 0; line-height: 1.5; color: #444a4b; text-align: center; max-width: 300px; margin-left: auto; margin-right: auto;">GigU Conta © 2025 Todos os direitos reservados.</p>
        <p style="box-sizing: border-box; margin: 0; line-height: 1.5; color: #444a4b; margin-top: 16px; text-align: center; max-width: 300px; margin-left: auto; margin-right: auto;">GigU Pagamentos Digitais está inscrita no CNPJ 29.316.027/0001-70.</p>

        <div class="links" style="box-sizing: border-box; margin-top: 20px;">
            <a href="https://use.giguconta.app" target="_blank" rel="noreferrer noopener" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">Acessar sua conta</a>
            <a href="https://giguconta.app/politica-de-privacidade" target="_blank" rel="noreferrer noopener" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b; margin-left: 12px;">Política de privacidade</a>
        </div>
    </div>
</div>
</body>
</html>